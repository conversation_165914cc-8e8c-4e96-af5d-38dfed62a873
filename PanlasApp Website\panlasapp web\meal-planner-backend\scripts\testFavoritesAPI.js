const mongoose = require('mongoose');
const User = require('../models/User');
const MealPlan = require('../models/MealPlan');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster';

async function testFavoritesAPI() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');

    // Simulate what the API does
    const user = await User.findOne({ email: '<EMAIL>' })
      .select('favoriteMealPlans')
      .populate('favoriteMealPlans.plan');
    
    if (!user) {
      console.log('User not found');
      return;
    }

    console.log('\n=== API Response Simulation ===');
    console.log('Response data:');
    
    const response = {
      success: true,
      favoriteMealPlans: user.favoriteMealPlans || []
    };
    
    console.log(JSON.stringify(response, null, 2));
    
    console.log('\n=== Frontend Processing ===');
    response.favoriteMealPlans.forEach((plan, index) => {
      console.log(`Plan ${index + 1}:`);
      console.log(`  - plan.name: "${plan.name}"`);
      console.log(`  - plan.date: "${plan.date}"`);
      console.log(`  - plan.plan: ${plan.plan ? 'exists' : 'null'}`);
      if (plan.plan) {
        console.log(`  - plan.plan.name: "${plan.plan.name}"`);
        console.log(`  - plan.plan.templateName: "${plan.plan.templateName}"`);
      }
      console.log('---');
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the test
testFavoritesAPI();
