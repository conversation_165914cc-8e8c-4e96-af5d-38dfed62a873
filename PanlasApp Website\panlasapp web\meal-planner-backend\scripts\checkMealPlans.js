const mongoose = require('mongoose');
require('dotenv').config();

// Get MongoDB URI from environment variable
const MONGODB_URI = process.env.MONGODB_URI;

console.log('Connecting to MongoDB...');

// Connect to MongoDB
mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(async () => {
  console.log('MongoDB connected successfully');
  
  try {
    // List all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('Available collections:');
    collections.forEach(collection => {
      console.log('- ' + collection.name);
    });
    
    // Find the MealPlan collection
    const mealPlanCollection = collections.find(c => 
      c.name.toLowerCase() === 'mealplans' || 
      c.name.toLowerCase() === 'meal_plans' || 
      c.name.toLowerCase() === 'mealplan'
    );
    
    if (!mealPlanCollection) {
      console.log('ERROR: MealPlan collection not found!');
      process.exit(1);
    }
    
    console.log('Found MealPlan collection:', mealPlanCollection.name);
    
    // Count all meal plans
    const count = await mongoose.connection.db.collection(mealPlanCollection.name).countDocuments();
    console.log(`Total meal plans in the database: ${count}`);
    
    // Get all meal plans
    const mealPlans = await mongoose.connection.db.collection(mealPlanCollection.name).find().toArray();
    console.log(`Retrieved ${mealPlans.length} meal plans`);
    
    // Check if meal plans have user field
    const mealPlansWithUser = mealPlans.filter(plan => plan.user);
    console.log(`Meal plans with user field: ${mealPlansWithUser.length} out of ${mealPlans.length}`);
    
    // Group meal plans by user
    const userMealPlans = {};
    mealPlans.forEach(plan => {
      const userId = plan.user ? plan.user.toString() : 'no-user';
      if (!userMealPlans[userId]) {
        userMealPlans[userId] = [];
      }
      userMealPlans[userId].push(plan.date);
    });
    
    console.log('Meal plans by user:');
    Object.keys(userMealPlans).forEach(userId => {
      console.log(`- User ${userId}: ${userMealPlans[userId].length} meal plans`);
    });
    
    // Find the User collection
    const userCollection = collections.find(c => 
      c.name.toLowerCase() === 'users' || 
      c.name.toLowerCase() === 'user'
    );
    
    if (!userCollection) {
      console.log('ERROR: User collection not found!');
      process.exit(1);
    }
    
    console.log('Found User collection:', userCollection.name);
    
    // Count all users
    const userCount = await mongoose.connection.db.collection(userCollection.name).countDocuments();
    console.log(`Total users in the database: ${userCount}`);
    
    // Get all users
    const users = await mongoose.connection.db.collection(userCollection.name)
      .find({}, { projection: { _id: 1, username: 1, email: 1 } })
      .toArray();
    console.log(`Retrieved ${users.length} users`);
    
    // Print user details
    console.log('User details:');
    users.forEach(user => {
      const userMealPlanCount = userMealPlans[user._id.toString()] ? userMealPlans[user._id.toString()].length : 0;
      console.log(`- User ID: ${user._id}, Username: ${user.username}, Email: ${user.email}, Meal Plans: ${userMealPlanCount}`);
    });
    
    // Check for the MealPlan model
    try {
      const MealPlan = mongoose.model('MealPlan');
      console.log('MealPlan model exists with collection name:', MealPlan.collection.collectionName);
      
      // Check if the model's collection name matches the actual collection
      if (MealPlan.collection.collectionName !== mealPlanCollection.name) {
        console.log('WARNING: MealPlan model collection name does not match actual collection!');
        console.log('This could be causing your issues with saving meal plans.');
      }
    } catch (err) {
      console.log('MealPlan model not registered yet');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking meal plans:', error);
    process.exit(1);
  }
});
