const mongoose = require('mongoose');
const MealPlan = require('../models/MealPlan');
const User = require('../models/User');
require('dotenv').config();

// Get MongoDB URI from environment variable
const MONGO_URI = process.env.MONGODB_URI; // Note: MONGODB_URI not MONGO_URI

console.log('Connecting to MongoDB...');
console.log('MongoDB URI:', MONGO_URI ? 'URI found' : 'URI not found');

// Connect to MongoDB
mongoose.connect(MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(() => {
  console.log('MongoDB connected successfully');
  migrateMealPlans();
}).catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

const migrateMealPlans = async () => {
  try {
    console.log('Starting meal plan migration...');
    
    // Get the first user (or create a default one if none exists)
    let defaultUser = await User.findOne();
    
    if (!defaultUser) {
      console.log('No users found. Creating a default user...');
      defaultUser = new User({
        username: 'default_user',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Default',
        lastName: 'User',
        gender: 'prefer-not-to-say',
        barangay: 'Not specified'
      });
      await defaultUser.save();
      console.log('Default user created with ID:', defaultUser._id);
    } else {
      console.log('Using existing user with ID:', defaultUser._id);
    }
    
    // Find all meal plans without a user field
    const mealPlansWithoutUser = await MealPlan.find({ user: { $exists: false } });
    console.log(`Found ${mealPlansWithoutUser.length} meal plans without a user field`);
    
    // Update each meal plan to add the user field
    for (const plan of mealPlansWithoutUser) {
      console.log(`Updating meal plan for date ${plan.date}...`);
      
      // Add the user field
      plan.user = defaultUser._id;
      
      // Save the updated meal plan
      await plan.save();
      console.log(`Updated meal plan for date ${plan.date}`);
    }
    
    console.log('Migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
};
