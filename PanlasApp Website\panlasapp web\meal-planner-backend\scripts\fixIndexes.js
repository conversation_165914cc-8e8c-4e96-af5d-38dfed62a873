const mongoose = require('mongoose');
require('dotenv').config();

// Get MongoDB URI from environment variable
const MONGO_URI = process.env.MONGODB_URI;

console.log('Connecting to MongoDB...');
console.log('MongoDB URI:', MONGO_URI ? 'URI found' : 'URI not found');

// Connect to MongoDB
mongoose.connect(MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(() => {
  console.log('MongoDB connected successfully');
  fixIndexes();
}).catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

const fixIndexes = async () => {
  try {
    console.log('Starting index fix...');

    // Get the collection
    const collection = mongoose.connection.db.collection('mealplans');

    // List all existing indexes
    console.log('Current indexes:');
    const indexes = await collection.listIndexes().toArray();
    indexes.forEach(index => {
      console.log(`- ${index.name}: ${JSON.stringify(index.key)}`);
    });

    // Check if there's a problematic index on just the date field (should not be unique)
    const dateOnlyIndex = indexes.find(index =>
      index.name === 'date_1' && index.unique === true
    );

    if (dateOnlyIndex) {
      console.log(`Found problematic unique date index: ${dateOnlyIndex.name}`);
      console.log('Dropping the problematic unique date index...');

      try {
        await collection.dropIndex(dateOnlyIndex.name);
        console.log(`Successfully dropped index: ${dateOnlyIndex.name}`);

        // Recreate it as non-unique
        await collection.createIndex({ date: 1 }, { unique: false });
        console.log('Recreated date index as non-unique');
      } catch (error) {
        console.log(`Could not fix date index:`, error.message);
      }
    }

    // Check if the problematic userId index exists
    const problematicIndex = indexes.find(index =>
      index.name.includes('userId_1_date_1') ||
      (index.key && index.key.userId !== undefined)
    );

    if (problematicIndex) {
      console.log(`Found problematic index: ${problematicIndex.name}`);
      console.log('Dropping the problematic index...');

      try {
        await collection.dropIndex(problematicIndex.name);
        console.log(`Successfully dropped index: ${problematicIndex.name}`);
      } catch (error) {
        console.log(`Could not drop index ${problematicIndex.name}:`, error.message);
      }
    } else {
      console.log('No problematic userId index found');
    }

    // Check if the correct index exists
    const correctIndex = indexes.find(index =>
      index.key && index.key.user !== undefined && index.key.date !== undefined
    );

    if (!correctIndex) {
      console.log('Creating correct index on user and date...');
      await collection.createIndex({ user: 1, date: 1 }, { unique: false });
      console.log('Successfully created index on user and date');
    } else {
      console.log('Correct index already exists');
    }

    // List indexes again to confirm
    console.log('\nFinal indexes:');
    const finalIndexes = await collection.listIndexes().toArray();
    finalIndexes.forEach(index => {
      console.log(`- ${index.name}: ${JSON.stringify(index.key)}`);
    });

    console.log('Index fix completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Index fix failed:', error);
    process.exit(1);
  }
};
