const fs = require('fs');
const path = require('path');

// Function to analyze meal and determine dietary types
const analyzeDietaryTypes = (meal) => {
  const dietType = {
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isDairyFree: false,
    isNutFree: false,
    isLowCarb: false,
    isKeto: false,
    isPescatarian: false,
    isHalal: false
  };

  // Convert ingredients to lowercase for easier checking
  const ingredients = meal.ingredients ? meal.ingredients.join(' ').toLowerCase() : '';
  const name = meal.name.toLowerCase();
  const description = meal.description ? meal.description.toLowerCase() : '';
  const dietaryTags = meal.dietaryTags ? meal.dietaryTags.join(' ').toLowerCase() : '';
  const allergens = meal.allergens || [];

  // Check dietary tags first (most reliable)
  if (dietaryTags.includes('vegan')) {
    dietType.isVegan = true;
    dietType.isVegetarian = true; // Vegan is also vegetarian
  } else if (dietaryTags.includes('vegetarian')) {
    dietType.isVegetarian = true;
  }

  if (dietaryTags.includes('gluten-free')) {
    dietType.isGlutenFree = true;
  }

  if (dietaryTags.includes('low-carb')) {
    dietType.isLowCarb = true;
  }

  // Use allergen data to determine dietary restrictions
  dietType.isGlutenFree = !allergens.includes('gluten');
  dietType.isDairyFree = !allergens.includes('dairy');
  dietType.isNutFree = !allergens.includes('nuts');

  // Meat indicators
  const meatKeywords = [
    'chicken', 'pork', 'beef', 'meat', 'manok', 'baboy', 'baka', 'lechon', 'tapa', 'tocino', 
    'longganisa', 'sisig', 'dinuguan', 'menudo', 'caldereta', 'afritada', 'inasal',
    'nilaga', 'tinola', 'oxtail', 'tripe'
  ];

  // Seafood indicators (actual seafood, not just fish sauce)
  const seafoodKeywords = [
    'bangus', 'tilapia', 'tuna', 'salmon', 'isda', 'hipon', 'pusit', 'alimango', 
    'daing', 'dried fish', 'tuyo', 'shrimp', 'crab', 'squid'
  ];

  // Check for meat (only if not already marked as vegan/vegetarian from tags)
  if (!dietType.isVegan && !dietType.isVegetarian) {
    const hasMeat = meatKeywords.some(keyword => 
      ingredients.includes(keyword) || name.includes(keyword)
    );

    // Check for actual seafood (not just fish sauce)
    const hasSeafood = seafoodKeywords.some(keyword => 
      ingredients.includes(keyword) || name.includes(keyword)
    );

    // Check for fish sauce separately (doesn't make it pescatarian, just not vegan)
    const hasFishSauce = allergens.includes('fish');

    if (!hasMeat && !hasSeafood) {
      dietType.isVegetarian = true;
      
      // Check if it's also vegan (no dairy, eggs, or fish sauce)
      const hasDairy = allergens.includes('dairy');
      const hasEggs = allergens.includes('eggs');
      
      if (!hasDairy && !hasEggs && !hasFishSauce) {
        dietType.isVegan = true;
      }
    } else if (!hasMeat && hasSeafood) {
      dietType.isPescatarian = true;
    }
  }

  // Low carb check (less than 20g carbs)
  if (meal.carbs && meal.carbs < 20) {
    dietType.isLowCarb = true;
  }
  
  // Keto check (very low carb, high fat)
  if (meal.carbs && meal.fat && meal.carbs < 10 && meal.fat > 15) {
    dietType.isKeto = true;
  }

  // Halal check (no pork, no alcohol)
  const porkKeywords = ['pork', 'baboy', 'lechon', 'tocino', 'longganisa', 'sisig'];
  const hasPork = porkKeywords.some(keyword => 
    ingredients.includes(keyword) || name.includes(keyword)
  );
  dietType.isHalal = !hasPork;

  return dietType;
};

// Function to add dietary types to all meals
const addDietaryTypesToMeals = () => {
  try {
    console.log('🔄 Adding dietary type information to Filipino meals...');
    
    // Read the current meals data
    const filePath = path.join(__dirname, 'filipinoMealData.json');
    const mealsData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    console.log(`📊 Processing ${mealsData.length} meals`);

    // Process each meal
    const updatedMeals = mealsData.map(meal => {
      // Add dietary type if not already present or update existing
      meal.dietType = analyzeDietaryTypes(meal);

      // Log special dietary meals
      const specialDiets = [];
      if (meal.dietType.isVegan) specialDiets.push('Vegan');
      if (meal.dietType.isVegetarian && !meal.dietType.isVegan) specialDiets.push('Vegetarian');
      if (meal.dietType.isPescatarian) specialDiets.push('Pescatarian');
      if (meal.dietType.isGlutenFree) specialDiets.push('Gluten-Free');
      if (meal.dietType.isDairyFree) specialDiets.push('Dairy-Free');
      if (meal.dietType.isNutFree) specialDiets.push('Nut-Free');
      if (meal.dietType.isLowCarb) specialDiets.push('Low-Carb');
      if (meal.dietType.isKeto) specialDiets.push('Keto');
      if (meal.dietType.isHalal) specialDiets.push('Halal');

      if (specialDiets.length > 0) {
        console.log(`🏷️  ${meal.name}: ${specialDiets.join(', ')}`);
      }

      return meal;
    });

    // Write back to file
    fs.writeFileSync(filePath, JSON.stringify(updatedMeals, null, 2));
    
    // Statistics
    const dietaryStats = {
      vegan: updatedMeals.filter(meal => meal.dietType.isVegan).length,
      vegetarian: updatedMeals.filter(meal => meal.dietType.isVegetarian).length,
      pescatarian: updatedMeals.filter(meal => meal.dietType.isPescatarian).length,
      glutenFree: updatedMeals.filter(meal => meal.dietType.isGlutenFree).length,
      dairyFree: updatedMeals.filter(meal => meal.dietType.isDairyFree).length,
      nutFree: updatedMeals.filter(meal => meal.dietType.isNutFree).length,
      lowCarb: updatedMeals.filter(meal => meal.dietType.isLowCarb).length,
      keto: updatedMeals.filter(meal => meal.dietType.isKeto).length,
      halal: updatedMeals.filter(meal => meal.dietType.isHalal).length
    };

    console.log('\n📈 Dietary Type Statistics:');
    console.log(`✅ Total meals processed: ${updatedMeals.length}`);
    console.log(`🌿 Vegan meals: ${dietaryStats.vegan}`);
    console.log(`🌱 Vegetarian meals: ${dietaryStats.vegetarian}`);
    console.log(`🐟 Pescatarian meals: ${dietaryStats.pescatarian}`);
    console.log(`🌾 Gluten-Free meals: ${dietaryStats.glutenFree}`);
    console.log(`🥛 Dairy-Free meals: ${dietaryStats.dairyFree}`);
    console.log(`🥜 Nut-Free meals: ${dietaryStats.nutFree}`);
    console.log(`🥩 Low-Carb meals: ${dietaryStats.lowCarb}`);
    console.log(`🥓 Keto meals: ${dietaryStats.keto}`);
    console.log(`☪️  Halal meals: ${dietaryStats.halal}`);

    console.log('\n🎉 Successfully added dietary type information to all meals!');
    
    return updatedMeals;

  } catch (error) {
    console.error('❌ Error adding dietary types to meals:', error);
    throw error;
  }
};

// Run the script
if (require.main === module) {
  addDietaryTypesToMeals();
}

module.exports = { addDietaryTypesToMeals, analyzeDietaryTypes };
