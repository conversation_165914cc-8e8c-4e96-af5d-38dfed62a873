export const colors = {
  primary: '#20C5AF',
  secondary: '#ff4f4f',
  background: '#f8f9fa',
  surface: '#ffffff',
  text: '#333333',
  textSecondary: '#666666',
  border: '#e0e0e0',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  light: '#f8f9fa',
  dark: '#333333',
};

// Responsive breakpoints
export const breakpoints = {
  small: 320,    // Small phones
  medium: 375,   // Medium phones (iPhone 6/7/8)
  large: 414,    // Large phones (iPhone 6+/7+/8+)
  xlarge: 768,   // Tablets
  xxlarge: 1024, // Large tablets
};

// Responsive font sizes
export const fonts = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  sizes: {
    small: {
      small: 10,
      medium: 12,
      large: 14,
      xlarge: 14,
      xxlarge: 14,
    },
    medium: {
      small: 12,
      medium: 14,
      large: 16,
      xlarge: 16,
      xxlarge: 16,
    },
    large: {
      small: 16,
      medium: 18,
      large: 20,
      xlarge: 20,
      xxlarge: 20,
    },
    xlarge: {
      small: 20,
      medium: 22,
      large: 24,
      xlarge: 24,
      xxlarge: 24,
    },
    xxlarge: {
      small: 26,
      medium: 28,
      large: 32,
      xlarge: 32,
      xxlarge: 32,
    },
  },
};

// Responsive spacing
export const spacing = {
  xs: {
    small: 2,
    medium: 3,
    large: 4,
    xlarge: 4,
    xxlarge: 4,
  },
  sm: {
    small: 4,
    medium: 6,
    large: 8,
    xlarge: 8,
    xxlarge: 8,
  },
  md: {
    small: 8,
    medium: 12,
    large: 16,
    xlarge: 16,
    xxlarge: 16,
  },
  lg: {
    small: 12,
    medium: 18,
    large: 24,
    xlarge: 24,
    xxlarge: 24,
  },
  xl: {
    small: 16,
    medium: 24,
    large: 32,
    xlarge: 32,
    xxlarge: 32,
  },
  xxl: {
    small: 24,
    medium: 36,
    large: 48,
    xlarge: 48,
    xxlarge: 48,
  },
};

export const borderRadius = {
  small: 4,
  medium: 8,
  large: 12,
  xlarge: 16,
  round: 50,
};

export const shadows = {
  small: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  large: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

// Legacy static values for backward compatibility
export const legacyFonts = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  sizes: {
    small: 12,
    medium: 16,
    large: 20,
    xlarge: 24,
    xxlarge: 32,
  },
};

export const legacySpacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const theme = {
  colors,
  fonts,
  spacing,
  borderRadius,
  shadows,
  // Legacy support
  legacyFonts,
  legacySpacing,
};
