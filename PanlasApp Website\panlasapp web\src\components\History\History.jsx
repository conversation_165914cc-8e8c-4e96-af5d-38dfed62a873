import React, { useEffect, useState } from "react";
import Layout from "../Layout/Layout";
import axios from "axios";
const RECENTLY_VIEWED_KEY = "recentlyViewedMeals";

// Convert price range to peso signs
const getPesoSigns = (priceRange) => {
  switch (priceRange) {
    case "Low": return "₱";
    case "Mid": return "₱₱";
    case "High": return "₱₱₱";
    default: return "₱";
  }
};

const History = () => {
  const [recentMeals, setRecentMeals] = useState([]);
  const [mealsFromSavedPlans, setMealsFromSavedPlans] = useState([]);
  const token = localStorage.getItem("token");

  // Fetch recently viewed meals
  useEffect(() => {
    if (!token) return;
    axios.get("http://localhost:5000/api/users/recently-viewed-meals", {
      headers: { Authorization: `Bearer ${token}` }
    })
      .then(res => setRecentMeals(res.data.recentlyViewedMeals || []))
      .catch(() => setRecentMeals([]));
  }, [token]);

  // Fetch meals from saved meal plans
  useEffect(() => {
    if (!token) return;
    axios.get("http://localhost:5000/api/users/meals-from-saved-plans", {
      headers: { Authorization: `Bearer ${token}` }
    })
      .then(res => setMealsFromSavedPlans(res.data.mealsFromSavedPlans || []))
      .catch(() => setMealsFromSavedPlans([]));
  }, [token]);

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format meal type for display
  const formatMealType = (mealType) => {
    return mealType.charAt(0).toUpperCase() + mealType.slice(1);
  };

  return (
    <Layout>
      <div className="main-content">
        <div className="container">
          <h1>Your Meal History</h1>

          {/* Meals from Saved Plans Section */}
          <div className="history-section">
            <h2>Recently Added to Meal Plans</h2>
            {mealsFromSavedPlans.length === 0 ? (
              <p>No meals from saved plans yet.</p>
            ) : (
              <div className="food-grid">
                {mealsFromSavedPlans.map((meal, index) => (
                  <div key={`${meal.name}-${index}`} className="food-card meal-plan-card">
                    <div className="food-card-image">
                      <img src={meal.image} alt={meal.name} />
                    </div>
                    <div className="food-card-content">
                      <h3>{meal.name}</h3>
                      <div className="food-card-meta">
                        <div className="category-tag">
                          <span>{meal.category}</span>
                        </div>
                        <div className="rating">
                          <span>{meal.rating} &#9733;</span>
                        </div>
                      </div>
                      <div className="meal-plan-info">
                        <div className="meal-plan-details">
                          <span className={`meal-type-badge meal-type-${meal.addedToMealType.toLowerCase()}`}>
                            {formatMealType(meal.addedToMealType)}
                          </span>
                          <span className="added-date">
                            Added {formatDate(meal.addedAt)}
                          </span>
                        </div>
                        <div className="planned-for">
                          Planned for: {new Date(meal.addedToDate).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric'
                          })}
                        </div>
                        {meal.planName && (
                          <div className="plan-name" style={{
                            fontSize: '0.85em',
                            color: '#666',
                            fontStyle: 'italic',
                            marginTop: '4px'
                          }}>
                            From: {meal.planName}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Recently Viewed Meals Section */}
          <div className="history-section">
            <h2>Recently Viewed Meals</h2>
            {recentMeals.length === 0 ? (
              <p>No recently viewed meals yet.</p>
            ) : (
              <div className="food-grid">
                {recentMeals.map((meal) => (
                  <div key={meal.id || meal._id} className="food-card">
                    <div className="food-card-image">
                      <img src={meal.image} alt={meal.name} />
                    </div>
                    <div className="food-card-content">
                      <h3>{meal.name}</h3>
                      <div className="food-card-meta">
                        <div className="category-tag">
                          <span>{meal.category}</span>
                        </div>
                        <div className="rating">
                          <span>{meal.rating} &#9733;</span>
                        </div>
                      </div>
                      <div className="food-card-price">
                        <span className="price-range">
                          {getPesoSigns(meal.priceRange)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default History;