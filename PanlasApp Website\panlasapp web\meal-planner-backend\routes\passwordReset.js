const express = require('express');
const crypto = require('crypto');
const User = require('../models/User');
const emailService = require('../services/emailService');

const router = express.Router();

// Store reset tokens temporarily (in production, use Redis or database)
const resetTokens = new Map();

// Clean up expired tokens every hour
setInterval(() => {
  const now = Date.now();
  for (const [token, data] of resetTokens.entries()) {
    if (now > data.expiresAt) {
      resetTokens.delete(token);
    }
  }
}, 60 * 60 * 1000); // 1 hour

// @route   POST /api/password-reset/forgot
// @desc    Send password reset email
// @access  Public
router.post('/forgot', async (req, res) => {
  try {
    console.log('\n🔐 ===== FORGOT PASSWORD REQUEST =====');
    console.log('Request body:', req.body);
    
    const { email } = req.body;

    // Validate email
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Check if user exists
    const user = await User.findOne({ email: email.toLowerCase() });
    
    if (!user) {
      // Don't reveal if user exists or not for security
      console.log(`❌ User not found for email: ${email}`);
      return res.status(200).json({
        success: true,
        message: 'If an account with that email exists, we have sent a password reset link.'
      });
    }

    console.log(`✅ User found: ${user.email} (${user.firstName} ${user.lastName})`);

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const expiresAt = Date.now() + (60 * 60 * 1000); // 1 hour from now

    // Store token with user info
    resetTokens.set(resetToken, {
      userId: user._id,
      email: user.email,
      expiresAt: expiresAt
    });

    console.log(`🔑 Generated reset token: ${resetToken}`);
    console.log(`⏰ Token expires at: ${new Date(expiresAt)}`);

    // Send password reset email
    try {
      const emailSent = await emailService.sendPasswordResetEmail(
        user.email,
        user.firstName || user.username,
        resetToken
      );

      if (emailSent) {
        console.log('✅ Password reset email sent successfully');
        res.status(200).json({
          success: true,
          message: 'Password reset email sent successfully. Please check your email.'
        });
      } else {
        console.log('❌ Failed to send password reset email');
        // Remove token if email failed
        resetTokens.delete(resetToken);
        res.status(500).json({
          success: false,
          message: 'Failed to send password reset email. Please try again.'
        });
      }
    } catch (emailError) {
      console.error('❌ Email service error:', emailError);
      // Remove token if email failed
      resetTokens.delete(resetToken);
      res.status(500).json({
        success: false,
        message: 'Failed to send password reset email. Please try again.'
      });
    }

    console.log('=====================================\n');

  } catch (error) {
    console.error('❌ Forgot password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error. Please try again.'
    });
  }
});

// @route   POST /api/password-reset/verify-token
// @desc    Verify reset token
// @access  Public
router.post('/verify-token', async (req, res) => {
  try {
    console.log('\n🔍 ===== VERIFY RESET TOKEN =====');
    console.log('Request body:', req.body);
    
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Reset token is required'
      });
    }

    // Check if token exists and is valid
    const tokenData = resetTokens.get(token);
    
    if (!tokenData) {
      console.log(`❌ Invalid token: ${token}`);
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    // Check if token is expired
    if (Date.now() > tokenData.expiresAt) {
      console.log(`⏰ Expired token: ${token}`);
      resetTokens.delete(token);
      return res.status(400).json({
        success: false,
        message: 'Reset token has expired. Please request a new one.'
      });
    }

    console.log(`✅ Valid token for user: ${tokenData.email}`);
    
    res.status(200).json({
      success: true,
      message: 'Token is valid',
      email: tokenData.email
    });

    console.log('===============================\n');

  } catch (error) {
    console.error('❌ Token verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error. Please try again.'
    });
  }
});

// @route   POST /api/password-reset/reset
// @desc    Reset password with token
// @access  Public
router.post('/reset', async (req, res) => {
  try {
    console.log('\n🔄 ===== RESET PASSWORD =====');
    console.log('Request body:', { ...req.body, newPassword: '[HIDDEN]' });
    
    const { token, newPassword } = req.body;

    // Validate input
    if (!token || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Reset token and new password are required'
      });
    }

    // Validate password strength
    if (newPassword.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long'
      });
    }

    // Check if token exists and is valid
    const tokenData = resetTokens.get(token);
    
    if (!tokenData) {
      console.log(`❌ Invalid token: ${token}`);
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    // Check if token is expired
    if (Date.now() > tokenData.expiresAt) {
      console.log(`⏰ Expired token: ${token}`);
      resetTokens.delete(token);
      return res.status(400).json({
        success: false,
        message: 'Reset token has expired. Please request a new one.'
      });
    }

    // Find user
    const user = await User.findById(tokenData.userId);
    
    if (!user) {
      console.log(`❌ User not found for token: ${token}`);
      resetTokens.delete(token);
      return res.status(400).json({
        success: false,
        message: 'User not found'
      });
    }

    console.log(`✅ Resetting password for user: ${user.email}`);

    // Update user password (let the User model's pre-save hook handle hashing)
    user.password = newPassword;
    user.lastLogin = new Date();
    await user.save();

    // Remove used token
    resetTokens.delete(token);

    console.log('✅ Password reset successfully');
    
    res.status(200).json({
      success: true,
      message: 'Password reset successfully. You can now login with your new password.'
    });

    console.log('===========================\n');

  } catch (error) {
    console.error('❌ Password reset error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error. Please try again.'
    });
  }
});

// @route   GET /api/password-reset/tokens
// @desc    Get active tokens (for debugging - remove in production)
// @access  Public (should be protected in production)
router.get('/tokens', (req, res) => {
  const tokens = Array.from(resetTokens.entries()).map(([token, data]) => ({
    token: token.substring(0, 8) + '...',
    email: data.email,
    expiresAt: new Date(data.expiresAt),
    isExpired: Date.now() > data.expiresAt
  }));

  res.json({
    success: true,
    activeTokens: tokens.length,
    tokens: tokens
  });
});

module.exports = router;
