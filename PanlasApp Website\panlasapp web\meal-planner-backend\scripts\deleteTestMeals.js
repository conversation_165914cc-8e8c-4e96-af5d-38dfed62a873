const mongoose = require('mongoose');
const Meal = require('../models/Meal');
require('dotenv').config();

async function deleteTestMeals() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    console.log('🔍 Searching for test meals...');
    
    // Find meals with names "test" or "test test" (case insensitive)
    const testMeals = await Meal.find({
      name: { 
        $in: [
          /^test$/i,
          /^test test$/i
        ]
      }
    });

    console.log(`Found ${testMeals.length} test meals to delete:`);
    testMeals.forEach(meal => {
      console.log(`- "${meal.name}" (ID: ${meal._id})`);
    });

    if (testMeals.length === 0) {
      console.log('✅ No test meals found to delete');
      return;
    }

    // Delete the test meals
    const deleteResult = await Meal.deleteMany({
      name: { 
        $in: [
          /^test$/i,
          /^test test$/i
        ]
      }
    });

    console.log(`🗑️  Successfully deleted ${deleteResult.deletedCount} test meals`);

    // Verify deletion
    const remainingTestMeals = await Meal.find({
      name: { 
        $in: [
          /^test$/i,
          /^test test$/i
        ]
      }
    });

    if (remainingTestMeals.length === 0) {
      console.log('✅ Verification: All test meals have been successfully removed');
    } else {
      console.log(`⚠️  Warning: ${remainingTestMeals.length} test meals still remain`);
    }

    // Show total meal count after deletion
    const totalMeals = await Meal.countDocuments();
    console.log(`📊 Total meals remaining in database: ${totalMeals}`);

  } catch (error) {
    console.error('❌ Error deleting test meals:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  deleteTestMeals();
}

module.exports = { deleteTestMeals };
