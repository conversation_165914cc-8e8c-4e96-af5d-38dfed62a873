import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './SystemHealth.css';

function SystemHealth() {
  const [healthData, setHealthData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds
  const [lastRefreshed, setLastRefreshed] = useState(null);

  const fetchHealthData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const config = {
        headers: {
          'x-auth-token': token
        }
      };

      const response = await axios.get('http://localhost:5000/api/admin/system/health', config);
      setHealthData(response.data);
      setLastRefreshed(new Date());
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch system health data');
      console.error('System health error:', err);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHealthData();
    
    // Set up interval for refreshing data
    const intervalId = setInterval(() => {
      fetchHealthData();
    }, refreshInterval * 1000);
    
    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [refreshInterval]);

  const handleRefreshChange = (e) => {
    const value = parseInt(e.target.value);
    setRefreshInterval(value);
  };

  const formatUptime = (seconds) => {
    const days = Math.floor(seconds / (3600 * 24));
    const hours = Math.floor((seconds % (3600 * 24)) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  };

  const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  const getStatusClass = (status) => {
    return status === 'healthy' ? 'status-healthy' : 'status-unhealthy';
  };

  const handleManualRefresh = () => {
    fetchHealthData();
  };

  if (loading && !healthData) return <div className="loading-health">Loading system health data...</div>;
  if (error && !healthData) return <div className="error-health">{error}</div>;

  return (
    <div className="system-health">
      <div className="health-header">
        <h2>System Health Monitor</h2>
        <div className="refresh-controls">
          <button 
            className="refresh-button" 
            onClick={handleManualRefresh}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh Now'}
          </button>
          <div className="refresh-interval">
            <label htmlFor="refresh-interval">Auto-refresh:</label>
            <select 
              id="refresh-interval" 
              value={refreshInterval} 
              onChange={handleRefreshChange}
            >
              <option value="10">10 seconds</option>
              <option value="30">30 seconds</option>
              <option value="60">1 minute</option>
              <option value="300">5 minutes</option>
            </select>
          </div>
        </div>
      </div>
      
      {lastRefreshed && (
        <div className="last-refreshed">
          Last updated: {lastRefreshed.toLocaleTimeString()}
        </div>
      )}
      
      {healthData && (
        <div className="health-content">
          <div className="health-section">
            <h3>Server Status</h3>
            <div className="health-grid">
              <div className="health-item">
                <div className="health-label">Status</div>
                <div className={`health-value ${getStatusClass(healthData.status)}`}>
                  {healthData.status === 'healthy' ? 'Healthy' : 'Unhealthy'}
                </div>
              </div>
              <div className="health-item">
                <div className="health-label">Uptime</div>
                <div className="health-value">{formatUptime(healthData.uptime)}</div>
              </div>
              <div className="health-item">
                <div className="health-label">Environment</div>
                <div className="health-value">{healthData.environment}</div>
              </div>
              <div className="health-item">
                <div className="health-label">Node.js Version</div>
                <div className="health-value">{healthData.nodeVersion}</div>
              </div>
            </div>
          </div>
          
          <div className="health-section">
            <h3>System Resources</h3>
            <div className="health-grid">
              <div className="health-item">
                <div className="health-label">CPU Usage</div>
                <div className="health-value">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ width: `${healthData.cpu.usage}%` }}
                    ></div>
                  </div>
                  <div className="progress-text">{healthData.cpu.usage}%</div>
                </div>
              </div>
              <div className="health-item">
                <div className="health-label">Memory Usage</div>
                <div className="health-value">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ width: `${healthData.memory.usagePercentage}%` }}
                    ></div>
                  </div>
                  <div className="progress-text">
                    {formatBytes(healthData.memory.used)} / {formatBytes(healthData.memory.total)} 
                    ({healthData.memory.usagePercentage}%)
                  </div>
                </div>
              </div>
              <div className="health-item">
                <div className="health-label">Disk Space</div>
                <div className="health-value">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ width: `${healthData.disk.usagePercentage}%` }}
                    ></div>
                  </div>
                  <div className="progress-text">
                    {formatBytes(healthData.disk.used)} / {formatBytes(healthData.disk.total)} 
                    ({healthData.disk.usagePercentage}%)
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="health-section">
            <h3>Database Status</h3>
            <div className="health-grid">
              <div className="health-item">
                <div className="health-label">Connection</div>
                <div className={`health-value ${getStatusClass(healthData.database.status)}`}>
                  {healthData.database.status === 'healthy' ? 'Connected' : 'Disconnected'}
                </div>
              </div>
              <div className="health-item">
                <div className="health-label">Response Time</div>
                <div className="health-value">{healthData.database.responseTime} ms</div>
              </div>
              <div className="health-item">
                <div className="health-label">Size</div>
                <div className="health-value">{formatBytes(healthData.database.size)}</div>
              </div>
              <div className="health-item">
                <div className="health-label">Collections</div>
                <div className="health-value">{healthData.database.collections}</div>
              </div>
            </div>
          </div>
          
          <div className="health-section">
            <h3>API Endpoints</h3>
            <table className="endpoints-table">
              <thead>
                <tr>
                  <th>Endpoint</th>
                  <th>Status</th>
                  <th>Response Time</th>
                  <th>Last Checked</th>
                </tr>
              </thead>
              <tbody>
                {healthData.endpoints.map((endpoint, index) => (
                  <tr key={index}>
                    <td>{endpoint.url}</td>
                    <td className={getStatusClass(endpoint.status)}>
                      {endpoint.status === 'healthy' ? 'OK' : 'Error'}
                    </td>
                    <td>{endpoint.responseTime} ms</td>
                    <td>{new Date(endpoint.lastChecked).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {healthData.errors && healthData.errors.length > 0 && (
            <div className="health-section errors-section">
              <h3>Recent Errors</h3>
              <div className="error-list">
                {healthData.errors.map((error, index) => (
                  <div key={index} className="error-item">
                    <div className="error-time">{new Date(error.timestamp).toLocaleString()}</div>
                    <div className="error-message">{error.message}</div>
                    {error.stack && (
                      <pre className="error-stack">{error.stack}</pre>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default SystemHealth;
