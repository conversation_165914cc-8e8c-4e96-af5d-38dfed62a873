const fs = require('fs');
const path = require('path');

// Read the current Filipino meal data
const filePath = path.join(__dirname, 'filipinoMealData.json');
const mealsData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

// Price ranges based on meal categories and complexity
const priceRanges = {
  'Meat': { min: 220, max: 450 },
  'Soup': { min: 180, max: 350 },
  'Noodles': { min: 150, max: 280 },
  'Stew': { min: 280, max: 520 },
  'Grilled': { min: 200, max: 380 },
  'Rice Meal': { min: 160, max: 320 },
  'Porridge': { min: 120, max: 250 },
  'Appetizer': { min: 100, max: 200 },
  'default': { min: 150, max: 300 }
};

// Function to generate realistic price based on meal characteristics
function generatePrice(meal) {
  const category = meal.category || 'default';
  const range = priceRanges[category] || priceRanges.default;
  
  // Base price from category range
  let basePrice = Math.floor(Math.random() * (range.max - range.min + 1)) + range.min;
  
  // Adjust based on prep time (longer prep = higher price)
  if (meal.prepTime > 90) basePrice += 50;
  else if (meal.prepTime > 60) basePrice += 30;
  else if (meal.prepTime > 45) basePrice += 20;
  
  // Adjust based on rating (higher rating = slightly higher price)
  if (meal.rating >= 4.8) basePrice += 20;
  else if (meal.rating >= 4.6) basePrice += 10;
  
  // Adjust based on protein content (high protein = higher price)
  if (meal.protein > 30) basePrice += 30;
  else if (meal.protein > 25) basePrice += 20;
  
  // Round to nearest 10
  return Math.round(basePrice / 10) * 10;
}

// Add price to each meal
const updatedMeals = mealsData.map(meal => {
  if (!meal.price) {
    meal.price = generatePrice(meal);
  }
  return meal;
});

// Write updated data back to file
fs.writeFileSync(filePath, JSON.stringify(updatedMeals, null, 2));

console.log('✅ Successfully added prices to all meals in filipinoMealData.json');
console.log(`📊 Updated ${updatedMeals.length} meals with price information`);

// Display sample prices
console.log('\n📋 Sample meal prices:');
updatedMeals.slice(0, 10).forEach(meal => {
  console.log(`   ${meal.name}: ₱${meal.price} (${meal.category})`);
});
