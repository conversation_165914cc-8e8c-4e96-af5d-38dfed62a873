import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors } from '../styles/theme';

const PasswordStrengthIndicator = ({ password }) => {
  const getPasswordStrength = (password) => {
    if (!password) return { strength: 'none', score: 0, label: '', color: '#E0E0E0' };
    
    let score = 0;
    let feedback = [];
    
    // Length check
    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('At least 8 characters');
    }
    
    // Uppercase check
    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('One uppercase letter');
    }
    
    // Lowercase check
    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('One lowercase letter');
    }
    
    // Number check
    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('One number');
    }
    
    // Special character check
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    } else {
      feedback.push('One special character');
    }
    
    // Determine strength
    let strength, label, color;
    if (score <= 2) {
      strength = 'weak';
      label = 'Weak';
      color = '#FF5252';
    } else if (score <= 3) {
      strength = 'medium';
      label = 'Medium';
      color = '#FF9800';
    } else {
      strength = 'strong';
      label = 'Strong';
      color = '#4CAF50';
    }
    
    return { strength, score, label, color, feedback };
  };

  const passwordInfo = getPasswordStrength(password);
  
  if (!password) return null;

  return (
    <View style={styles.container}>
      <View style={styles.strengthContainer}>
        <Text style={styles.strengthLabel}>Password Strength: </Text>
        <Text style={[styles.strengthValue, { color: passwordInfo.color }]}>
          {passwordInfo.label}
        </Text>
      </View>
      
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { 
                width: `${(passwordInfo.score / 5) * 100}%`,
                backgroundColor: passwordInfo.color 
              }
            ]} 
          />
        </View>
      </View>
      
      {passwordInfo.feedback.length > 0 && (
        <View style={styles.feedbackContainer}>
          <Text style={styles.feedbackTitle}>Missing:</Text>
          {passwordInfo.feedback.map((item, index) => (
            <Text key={index} style={styles.feedbackItem}>• {item}</Text>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    marginBottom: 8,
  },
  strengthContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  strengthLabel: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  strengthValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  feedbackContainer: {
    marginTop: 4,
  },
  feedbackTitle: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
    marginBottom: 2,
  },
  feedbackItem: {
    fontSize: 12,
    color: colors.textSecondary,
    marginLeft: 8,
  },
});

export default PasswordStrengthIndicator;
