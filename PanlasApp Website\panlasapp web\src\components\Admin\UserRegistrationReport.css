.user-registration-report {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.user-registration-report h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
}

.report-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.date-range {
  display: flex;
  gap: 1rem;
}

.date-input {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.date-input label {
  font-size: 0.9rem;
  color: #555;
}

.date-input input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.generate-btn {
  padding: 0.5rem 1.5rem;
  background-color: #4CAF50; /* Match your app's primary color */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  align-self: flex-end;
}

.generate-btn:hover {
  background-color: #45a049;
}

.generate-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.export-btn {
  padding: 0.5rem 1.5rem;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.export-btn:hover {
  background-color: #0b7dda;
}

.report-error {
  color: #f44336;
  margin-bottom: 1rem;
}

.report-results {
  margin-top: 2rem;
}

.report-summary {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.report-summary h3 {
  margin-top: 0;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
}

.report-table th,
.report-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.report-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #333;
}

.report-table tr:hover {
  background-color: #f9f9f9;
}

.no-data {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

@media (max-width: 768px) {
  .report-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .date-range {
    flex-direction: column;
  }
  
  .report-table {
    font-size: 0.9rem;
  }
  
  .report-table th,
  .report-table td {
    padding: 0.5rem;
  }
}
