const mongoose = require('mongoose');

const analyticsSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  sessionId: {
    type: String,
    required: true
  },
  event: {
    type: String,
    required: true,
    enum: [
      'login',
      'logout',
      'signup',
      'profile_update',
      'meal_plan_create',
      'meal_plan_update',
      'meal_plan_delete',
      'meal_view',
      'meal_favorite',
      'meal_unfavorite',
      'search',
      'filter_apply',
      'page_view',
      'api_call',
      'error',
      'feedback_submit',
      'password_reset_request',
      'password_reset_complete',
      'email_verification',
      'admin_action'
    ]
  },
  eventData: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  // Device and Platform Information
  deviceInfo: {
    userAgent: String,
    platform: {
      type: String,
      enum: ['mobile', 'tablet', 'desktop', 'unknown'],
      default: 'unknown'
    },
    browser: String,
    browserVersion: String,
    os: String,
    osVersion: String,
    isMobile: {
      type: Boolean,
      default: false
    },
    screenResolution: String,
    viewport: String
  },
  // Location and Network
  location: {
    ipAddress: String,
    country: String,
    region: String,
    city: String,
    timezone: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  // Performance Metrics
  performance: {
    loadTime: Number,
    responseTime: Number,
    pageSize: Number
  },
  // User Context
  userContext: {
    isAdmin: {
      type: Boolean,
      default: false
    },
    isActive: {
      type: Boolean,
      default: true
    },
    isVerified: {
      type: Boolean,
      default: false
    },
    userType: {
      type: String,
      enum: ['admin', 'user'],
      default: 'user'
    },
    accountAge: Number, // in days
    lastLoginDays: Number // days since last login
  },
  // Session Information
  sessionInfo: {
    duration: Number, // in seconds
    pageViews: {
      type: Number,
      default: 1
    },
    isNewSession: {
      type: Boolean,
      default: true
    },
    referrer: String,
    entryPage: String,
    exitPage: String
  },
  // API Specific Data
  apiInfo: {
    endpoint: String,
    method: String,
    statusCode: Number,
    responseTime: Number,
    errorMessage: String
  },
  // Timestamps
  timestamp: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for better query performance
analyticsSchema.index({ user: 1, timestamp: -1 });
analyticsSchema.index({ event: 1, timestamp: -1 });
analyticsSchema.index({ 'userContext.isAdmin': 1, timestamp: -1 });
analyticsSchema.index({ 'userContext.isActive': 1, timestamp: -1 });
analyticsSchema.index({ 'deviceInfo.platform': 1, timestamp: -1 });
analyticsSchema.index({ 'deviceInfo.isMobile': 1, timestamp: -1 });
analyticsSchema.index({ sessionId: 1 });
analyticsSchema.index({ timestamp: -1 });
analyticsSchema.index({ createdAt: -1 });

// Compound indexes for common queries
analyticsSchema.index({ 
  'userContext.isAdmin': 1, 
  'userContext.isActive': 1, 
  timestamp: -1 
});

analyticsSchema.index({ 
  event: 1, 
  'userContext.userType': 1, 
  timestamp: -1 
});

analyticsSchema.index({ 
  'deviceInfo.platform': 1, 
  'userContext.userType': 1, 
  timestamp: -1 
});

// Static methods for analytics queries
analyticsSchema.statics.getUserAnalytics = function(userId, startDate, endDate) {
  return this.find({
    user: userId,
    timestamp: {
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ timestamp: -1 });
};

analyticsSchema.statics.getEventAnalytics = function(event, startDate, endDate) {
  return this.find({
    event: event,
    timestamp: {
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ timestamp: -1 });
};

analyticsSchema.statics.getPlatformAnalytics = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        timestamp: {
          $gte: startDate,
          $lte: endDate
        }
      }
    },
    {
      $group: {
        _id: '$deviceInfo.platform',
        count: { $sum: 1 },
        uniqueUsers: { $addToSet: '$user' }
      }
    },
    {
      $project: {
        platform: '$_id',
        count: 1,
        uniqueUsers: { $size: '$uniqueUsers' }
      }
    }
  ]);
};

const Analytics = mongoose.model('Analytics', analyticsSchema);

module.exports = Analytics;
