/* Landering Header */
.landing-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #20C5AF;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 15px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #333;
  font-weight: 700;
  font-size: 1.5rem;
}

.logo img {
  height: 40px;
  margin-right: 10px;
}

.main-nav ul {
  display: flex;
  list-style: none;
  align-items: center;
  font-family: "Roboto Condensed", sans-serif;
}

.main-nav ul li {
    margin-left: 25px;
    display: flex;
    align-items: center;
}

.main-nav ul li a {
  color: #ffffff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.22s cubic-bezier(0.4, 0, 0.2, 1), background 0.22s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Primary button color */
.main-nav ul li a.btn-login,
.main-nav ul li a.btn-signup {
  background-color: #ff9800;
  color: #fff !important;
  padding: 8px 22px;
  border-radius: 6px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15);
  border: none;
  transition: 
    background 0.22s cubic-bezier(0.4,0,0.2,1),
    color 0.22s cubic-bezier(0.4,0,0.2,1),
    box-shadow 0.22s cubic-bezier(0.4,0,0.2,1),
    transform 0.22s cubic-bezier(0.4,0,0.2,1);
  position: relative;
  outline: none;
}

/* Slightly darker on hover/focus */
.main-nav ul li a.btn-login:hover,
.main-nav ul li a.btn-login:focus,
.main-nav ul li a.btn-signup:hover,
.main-nav ul li a.btn-signup:focus {
  background-color: #fb8c00; /* Slightly darker than #ff9800 */
  color: #fff !important;
  box-shadow: 0 2px 8px 0 rgba(255,152,0,0.18);
  transform: translateY(-2px) scale(1.04);
}

/* Button press effect */
.main-nav ul li a.btn-login:active,
.main-nav ul li a.btn-signup:active {
  transform: scale(0.98);
}

.btn-login {
  color: #ffffff !important;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  height: 48px;
  width: 48px;
  padding: 0;
  margin-left: 10px;
}

.hamburger {
  display: block;
  position: relative;
  width: 28px;
  height: 3px;
  background-color: #333;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
  margin: 0 auto;
}

.hamburger:before,
.hamburger:after {
  content: '';
  position: absolute;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #333;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
}

.hamburger:before {
  top: -9px;
}

.hamburger:after {
  top: 9px;
}

.hamburger.active {
  background-color: transparent;
}

.hamburger.active:before {
  transform: rotate(45deg);
  top: 0;
}

.hamburger.active:after {
  transform: rotate(-45deg);
  top: 0;
}

.logo-container {
  position: relative;
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-text-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 0.5rem;
}

.logo-text {
  font-size: 2rem;
  font-weight: bold;
  line-height: 1.1;
}

.meal-planning-tag {
  font-size: 0.7rem;
  color: #ffffff;
  font-weight: 400;
  letter-spacing: 0.03em;
  border-radius: 8px;
  white-space: nowrap;
  pointer-events: none;
  background: none;
  padding: 0;
  margin-top: 0.1em;
}





@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex;
    z-index: 1001;
  }

  .main-nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    max-width: 300px;
    height: 100vh;
    background-color: #e6fcf9;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    transition: right 0.3s cubic-bezier(0.4,0,0.2,1);
    padding: 80px 20px 20px;
    z-index: 1000;
    font-family: "Roboto Condensed", sans-serif;
  }
  
  .main-nav.active {
    right: 0;
  }

  .main-nav ul {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    padding: 0;
  }

  .main-nav ul li {
    margin: 0 0 20px 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }

  .main-nav ul li a {
    width: 100%;
    box-sizing: border-box;
    padding: 12px 0;
    font-size: 1.1rem;
    text-align: center;
    border-radius: 6px;
    margin: 0;
    transition: color 0.22s cubic-bezier(0.4,0,0.2,1), background 0.22s cubic-bezier(0.4,0,0.2,1);
  }

  .main-nav ul li a:hover {
    color: #ffffff;
  }

  .main-nav ul li:last-child {
    margin-top: 20px;
  }

  .main-nav ul li a.btn {
    display: block;
    text-align: center;
    transition: 
      background 0.22s cubic-bezier(0.4,0,0.2,1),
      color 0.22s cubic-bezier(0.4,0,0.2,1),
      box-shadow 0.22s cubic-bezier(0.4,0,0.2,1),
      transform 0.22s cubic-bezier(0.4,0,0.2,1);
  }

  .main-nav ul li a.btn-login,
  .main-nav ul li a.btn-signup {
    background-color: #ff9800;
    color: #fff !important;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 0;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15);
    border: none;
    outline: none;
    width: 100%;
    padding: 12px 0;
    font-size: 1.1rem;
  }

  .main-nav ul li a.btn-login:hover,
  .main-nav ul li a.btn-login:focus,
  .main-nav ul li a.btn-signup:hover,
  .main-nav ul li a.btn-signup:focus {
    background-color: #fb8c00;
    color: #fff !important;
    box-shadow: 0 2px 8px 0 rgba(255,152,0,0.18);
    transform: translateY(-2px) scale(1.04);
  }

  .main-nav ul li a.btn-login:active,
  .main-nav ul li a.btn-signup:active {
    transform: scale(0.98);
  }
  
}
