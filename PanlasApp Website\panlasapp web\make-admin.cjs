// Give regular admin access to mum<PERSON><PERSON><PERSON><PERSON><PERSON><EMAIL>
const { MongoClient } = require('mongodb');

const MONGODB_URI = 'mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster';

async function makeAdmin() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('mealplanner');
    const usersCollection = db.collection('users');
    const adminsCollection = db.collection('admins');
    
    const email = '<EMAIL>';
    
    console.log('🔍 Making user admin...');
    
    // Find the user
    const user = await usersCollection.findOne({ email: email });
    
    if (!user) {
      console.log('❌ User not found with email:', email);
      return;
    }
    
    console.log('✅ User found:', user.username, user.email);
    
    // Update user to have admin privileges
    await usersCollection.updateOne(
      { _id: user._id },
      { 
        $set: { 
          isAdmin: true,
          updatedAt: new Date()
        }
      }
    );
    console.log('✅ Updated user to have isAdmin: true');
    
    // Check if admin record already exists
    const existingAdminRecord = await adminsCollection.findOne({ user: user._id });
    
    if (existingAdminRecord) {
      console.log('📋 Admin record already exists, updating...');
      await adminsCollection.updateOne(
        { user: user._id },
        {
          $set: {
            role: 'admin',
            permissions: ['user_management', 'analytics_view', 'system_health'],
            isActive: true,
            lastActivity: new Date(),
            updatedAt: new Date()
          }
        }
      );
      console.log('✅ Updated existing admin record');
    } else {
      console.log('📝 Creating new admin record...');
      
      // Create new admin record
      const newAdminRecord = {
        user: user._id,
        role: 'admin',
        permissions: ['user_management', 'analytics_view', 'system_health'],
        isActive: true,
        lastActivity: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await adminsCollection.insertOne(newAdminRecord);
      console.log('✅ Created new admin record');
    }
    
    // Final verification
    console.log('\n🔍 Final verification...');
    const finalUser = await usersCollection.findOne({ email: email });
    const finalAdmin = await adminsCollection.findOne({ user: user._id });
    
    console.log('👤 User Status:');
    console.log('  - ID:', finalUser._id);
    console.log('  - Username:', finalUser.username);
    console.log('  - Email:', finalUser.email);
    console.log('  - isAdmin:', finalUser.isAdmin);
    
    if (finalAdmin) {
      console.log('🛡️  Admin Status:');
      console.log('  - Role:', finalAdmin.role);
      console.log('  - Permissions:', finalAdmin.permissions);
      console.log('  - Active:', finalAdmin.isActive);
    }
    
    console.log('\n🎉 Admin access granted successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: 132fast@!');
    console.log('👑 Role: admin (regular admin, not super admin)');
    console.log('\n✅ User can now access the admin dashboard!');
    
  } catch (error) {
    console.error('❌ Error granting admin access:', error);
  } finally {
    await client.close();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

makeAdmin();
