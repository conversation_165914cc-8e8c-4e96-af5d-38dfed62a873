const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster');
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

// Update all users to have isActive: true by default
const updateUserStatus = async () => {
  try {
    console.log('Starting user status update...');
    
    // Update all users that don't have isActive field or have it as undefined/null
    const result = await User.updateMany(
      { 
        $or: [
          { isActive: { $exists: false } },
          { isActive: null },
          { isActive: undefined }
        ]
      },
      { 
        $set: { 
          isActive: true 
        }
      }
    );
    
    console.log(`Updated ${result.modifiedCount} users to have isActive: true`);
    
    // Get count of all users and their status
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: true });
    const inactiveUsers = await User.countDocuments({ isActive: false });
    
    console.log(`\nUser Status Summary:`);
    console.log(`Total Users: ${totalUsers}`);
    console.log(`Active Users: ${activeUsers}`);
    console.log(`Inactive Users: ${inactiveUsers}`);
    
    console.log('\nUser status update completed successfully!');
    
  } catch (error) {
    console.error('Error updating user status:', error);
  }
};

// Main function
const main = async () => {
  await connectDB();
  await updateUserStatus();
  await mongoose.connection.close();
  console.log('Database connection closed.');
  process.exit(0);
};

// Run the script
main();
