import React, { useState } from "react";
import { Link } from "react-router-dom";
import '../../../src/App.css';
import Header from "../Header/Header";
import Layout from "../Layout/Layout";
import FeedbackModal from "../Feedback/FeedbackModal";
import './Helpcenter.css';

const Helpcenter = () => {
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  const handleFeedbackSubmit = async (feedbackData) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5000/api/feedback/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify(feedbackData)
      });

      if (response.ok) {
        setFeedbackSubmitted(true);
        setTimeout(() => setFeedbackSubmitted(false), 3000);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to submit feedback');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  };

  return (
    <Layout>
      <div className="main-content">
        <div className="container">
          <div className="help-center-header">
            <h1>HELP CENTER</h1>
            <button
              className="feedback-button"
              onClick={() => setShowFeedbackModal(true)}
            >
              📝 Send Feedback
            </button>
          </div>

          {feedbackSubmitted && (
            <div className="feedback-success-message">
              ✅ Thank you for your feedback! We appreciate your input.
            </div>
          )}

          <div className="help-content">
            <section className="help-section">
              <h2>Welcome to PanlasApp Help Center</h2>
              <p>Find answers to common questions and get help with using PanlasApp for your meal planning needs.</p>
            </section>

            <section className="help-section">
              <h3>Getting Started</h3>
              <div className="help-item">
                <h4>How to create your first meal plan?</h4>
                <p>Navigate to the Meal Plan section and start by selecting your preferred meals for each day. You can customize your plan based on your dietary preferences and family size.</p>
              </div>
              <div className="help-item">
                <h4>Setting up your dietary preferences</h4>
                <p>Go to your Profile section to set up dietary restrictions, allergies, and nutritional goals. This helps us provide better meal recommendations.</p>
              </div>
            </section>

            <section className="help-section">
              <h3>Meal Planning Features</h3>
              <div className="help-item">
                <h4>How to save favorite meals?</h4>
                <p>Click the heart icon on any meal card to add it to your favorites. Access your saved meals from the Favorites section.</p>
              </div>
              <div className="help-item">
                <h4>Family meal planning</h4>
                <p>Add family members in the Family section and set individual dietary preferences for each member to create personalized meal plans.</p>
              </div>
            </section>

            <section className="help-section">
              <h3>Account Management</h3>
              <div className="help-item">
                <h4>How to update your profile?</h4>
                <p>Visit the Profile section to update your personal information, dietary preferences, and account settings.</p>
              </div>
              <div className="help-item">
                <h4>Changing your password</h4>
                <p>In your Profile section, you'll find an option to change your password securely.</p>
              </div>
            </section>

            <section className="help-section">
              <h3>Need More Help?</h3>
              <p>Can't find what you're looking for? We'd love to hear from you! Use the feedback button above to:</p>
              <ul>
                <li>Report bugs or technical issues</li>
                <li>Request new features</li>
                <li>Share your experience with PanlasApp</li>
                <li>Ask questions not covered in this help center</li>
                <li>Suggest improvements to our meal recommendations</li>
              </ul>
            </section>
          </div>
        </div>
      </div>

      <FeedbackModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        onSubmit={handleFeedbackSubmit}
      />
    </Layout>
  );
};
  export default Helpcenter;
