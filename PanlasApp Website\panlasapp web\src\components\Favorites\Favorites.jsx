import React, { useState } from "react";
import Header from "../Header/Header";
import Sidebar from "../Sidebar/Sidebar";
import { FaHeart, FaTimes, FaTrash, FaCalendarAlt, FaUtensils, FaClock } from "react-icons/fa";
import "../../../src/App.css";
import { useFavorites } from "../../components/Favorites/FavoritesContext";

const Favorites = () => {
  const [sidebarActive, setSidebarActive] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState(null);
  const [selectedMealPlan, setSelectedMealPlan] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [showMealPlanModal, setShowMealPlanModal] = useState(false);
  const [activeTab, setActiveTab] = useState('meals'); // 'meals' or 'mealplans'
  const [notification, setNotification] = useState({ show: false, message: "" });
  const [loadingMealPlanDetails, setLoadingMealPlanDetails] = useState(false);
  const [mealPlanDetails, setMealPlanDetails] = useState(null);

  const { favorites, favoriteMealPlans, removeFavorite, removeFavoriteMealPlan } = useFavorites();


  
  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  const handleRemoveFavorite = (e, dishId) => {
    e.stopPropagation();
    removeFavorite(dishId);
    showNotification("Removed from favorites");
  };

  const handleRemoveFavoriteMealPlan = async (e, favPlan) => {
    e.stopPropagation();
    // Use the favorite entry's _id, not the plan's _id
    const favoriteId = favPlan._id || favPlan.id;
    console.log('Removing favorite meal plan with ID:', favoriteId);
    const result = await removeFavoriteMealPlan(favoriteId);
    if (result.success) {
      showNotification("Meal plan removed from favorites");
    } else {
      showNotification("Failed to remove meal plan from favorites");
    }
  };

  const showNotification = (message) => {
    setNotification({ show: true, message });
    setTimeout(() => {
      setNotification({ show: false, message: "" });
    }, 3000);
  };

  const openMealDetails = (dish) => {
    setSelectedMeal(dish);
    setShowModal(true);
  };

  const closeMealDetails = () => {
    setShowModal(false);
  };

  const fetchMealPlanDetails = async (mealPlanId) => {
    try {
      setLoadingMealPlanDetails(true);
      const token = localStorage.getItem("token");

      // First, get the specific meal plan to get its template name
      const singlePlanResponse = await fetch(`http://localhost:5000/api/meal-plans/saved/${mealPlanId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (!singlePlanResponse.ok) {
        return null;
      }

      const singlePlan = await singlePlanResponse.json();
      console.log('Single plan data:', singlePlan);

      // If this plan has a templateName, fetch all meal plans with the same templateName
      if (singlePlan.templateName) {
        // Use the general meal plans endpoint to get all meal plans for the user
        const allPlansResponse = await fetch(`http://localhost:5000/api/meal-plans`, {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (allPlansResponse.ok) {
          const allPlans = await allPlansResponse.json();
          console.log('All meal plans:', allPlans);

          // Filter plans with the same templateName
          const relatedPlans = allPlans.filter(plan =>
            plan.templateName === singlePlan.templateName
          );

          console.log('Related plans with same templateName:', relatedPlans);

          // Combine all meals from related plans
          const allMeals = [];
          relatedPlans.forEach(plan => {
            // Transform the meal plan data to include meals from meal type arrays
            const mealTypes = ['breakfast', 'lunch', 'dinner'];

            mealTypes.forEach(mealType => {
              if (plan[mealType] && plan[mealType].length > 0) {
                plan[mealType].forEach(meal => {
                  allMeals.push({
                    date: plan.date,
                    mealType: mealType,
                    mealData: {
                      name: meal.name,
                      calories: meal.calories,
                      protein: meal.protein,
                      carbs: meal.carbs,
                      fat: meal.fat,
                      image: meal.image,
                      description: meal.description,
                      ingredients: meal.ingredients,
                      instructions: meal.instructions,
                      category: meal.category,
                      dietaryTags: meal.dietaryTags
                    }
                  });
                });
              }
            });
          });

          console.log('Combined meals from all related plans:', allMeals);

          return {
            ...singlePlan,
            meals: allMeals,
            relatedPlans: relatedPlans
          };
        }
      }

      // Fallback to single plan if no template name or error
      return singlePlan;
    } catch (error) {
      console.error('Error fetching meal plan details:', error);
      return null;
    } finally {
      setLoadingMealPlanDetails(false);
    }
  };

  const openMealPlanDetails = async (mealPlan) => {
    console.log('Opening meal plan details for:', mealPlan);
    setSelectedMealPlan(mealPlan);
    setShowMealPlanModal(true);

    // If this is a favorite meal plan with a plan reference, fetch the detailed data
    if (mealPlan.plan) {
      const planId = typeof mealPlan.plan === 'string' ? mealPlan.plan : mealPlan.plan._id;
      console.log('Fetching details for plan ID:', planId);
      const detailedPlan = await fetchMealPlanDetails(planId);
      console.log('Fetched detailed plan:', detailedPlan);
      if (detailedPlan) {
        setMealPlanDetails(detailedPlan);
      }
    }
  };

  const closeMealPlanDetails = () => {
    setShowMealPlanModal(false);
    setMealPlanDetails(null);
    setSelectedMealPlan(null);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="app-container">
      <Header toggleSidebar={toggleSidebar} />
      <Sidebar isActive={sidebarActive} />
      <div className="content-area">
        <div className="main-content">
          <div className="container">
            <h1>MY FAVORITES</h1>

            {/* Notification */}
            {notification.show && (
              <div className="notification">{notification.message}</div>
            )}

            {/* Tabs */}
            <div className="favorites-tabs">
              <button
                className={`tab-btn ${activeTab === 'meals' ? 'active' : ''}`}
                onClick={() => setActiveTab('meals')}
              >
                <FaUtensils /> Favorite Meals ({favorites?.length || 0})
              </button>
              <button
                className={`tab-btn ${activeTab === 'mealplans' ? 'active' : ''}`}
                onClick={() => setActiveTab('mealplans')}
              >
                <FaCalendarAlt /> Favorite Meal Plans ({favoriteMealPlans?.length || 0})
              </button>
            </div>

            {/* Meals Tab Content */}
            {activeTab === 'meals' && (
              <>
                {favorites && favorites.length > 0 ? (
                  <div className="food-grid">
                    {favorites.map((dish) => (
                      <div key={dish.id || dish._id} className="food-card">
                        <div className="food-card-image">
                          <img src={dish.image} alt={dish.name} />
                          <button
                            className="favorite-btn"
                            onClick={(e) =>
                              handleRemoveFavorite(e, dish.id || dish._id)
                            }
                            title="Remove from favorites"
                          >
                            <FaTrash className="trash-icon" />
                          </button>
                        </div>
                        <div className="food-card-content">
                          <h3>{dish.name}</h3>
                          <div className="food-card-meta">
                            <div className="category-tag">
                              <span>{dish.category}</span>
                            </div>
                            <div className="rating">
                              <span>{dish.rating} &#9733;</span>
                            </div>
                          </div>
                          <div className="food-card-price">
                            <span className="price-range">
                              {dish.priceRange} Range
                            </span>
                          </div>
                          <button
                            className="view-meal-btn"
                            onClick={() => openMealDetails(dish)}
                          >
                            View Meal
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="no-favorites">
                    <h2>You haven't added any favorite meals yet</h2>
                    <p>
                      Click the heart icon on meals you like to add them to your
                      favorites
                    </p>
                  </div>
                )}
              </>
            )}

            {/* Meal Plans Tab Content */}
            {activeTab === 'mealplans' && (
              <>
                {favoriteMealPlans && favoriteMealPlans.length > 0 ? (
                  <div className="meal-plans-grid">
                    {favoriteMealPlans.map((favPlan) => {
                      const plan = favPlan.plan || favPlan;
                      // Use the name from the favorite entry, or fall back to templateName from the plan
                      const planName = favPlan.name || plan.templateName || plan.name || 'Unnamed Meal Plan';
                      // Use the date from the favorite entry, or fall back to plan date
                      const planDate = favPlan.date || plan.date;
                      return (
                        <div key={plan._id || plan.id} className="meal-plan-card">
                          <div className="meal-plan-header">
                            <h3>{planName}</h3>
                            <button
                              className="favorite-btn"
                              onClick={(e) =>
                                handleRemoveFavoriteMealPlan(e, favPlan)
                              }
                              title="Remove from favorites"
                            >
                              <FaTrash className="trash-icon" />
                            </button>
                          </div>
                          <div className="meal-plan-content">
                            <div className="meal-plan-meta">
                              <div className="meta-item">
                                <FaCalendarAlt />
                                <span>{planDate ? formatDate(planDate) : 'Invalid Date'}</span>
                              </div>
                              <div className="meta-item">
                                <FaUtensils />
                                <span>{favPlan.totalMeals || plan.totalMeals || 0} meals planned</span>
                              </div>
                              {(favPlan.mealTypes || plan.mealTypes) && (favPlan.mealTypes || plan.mealTypes).length > 0 && (
                                <div className="meta-item">
                                  <span className="dietary-preference">{(favPlan.mealTypes || plan.mealTypes).join(', ')}</span>
                                </div>
                              )}
                            </div>
                            <button
                              className="view-meal-plan-btn"
                              onClick={() => openMealPlanDetails(favPlan)}
                            >
                              View Meal Plan
                            </button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="no-favorites">
                    <h2>You haven't added any favorite meal plans yet</h2>
                    <p>
                      Create meal plans and add them to favorites to see them here
                    </p>
                  </div>
                )}
              </>
            )}
            {/* Meal Details Modal */}
            {showModal && selectedMeal && (
              <div className="modal-overlay">
                <div className="modal-content">
                  <div className="modal-header">
                    <h2>{selectedMeal.name}</h2>
                    <button className="close-modal" onClick={closeMealDetails}>
                      <FaTimes />
                    </button>
                  </div>
                  <div className="modal-body">
                    <div className="meal-image">
                      <img src={selectedMeal.image} alt={selectedMeal.name} />
                    </div>
                    <div className="meal-details">
                      <p className="meal-description">
                        {selectedMeal.description}
                      </p>
                      <div className="meal-meta">
                        <span className="meal-rating">
                          {selectedMeal.rating} &#9733;
                        </span>
                        <span className="meal-category">
                          {selectedMeal.category}
                        </span>
                        <span className="meal-price">
                          Calories: {selectedMeal.calories} (
                          {selectedMeal.priceRange} Range)
                        </span>
                      </div>
                      {selectedMeal.ingredients && (
                        <div className="meal-ingredients">
                          <h3>Ingredients</h3>
                          <ul>
                            {selectedMeal.ingredients.map((ingredient, idx) => (
                              <li key={idx}>{ingredient}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {selectedMeal.steps && (
                        <div className="meal-steps">
                          <h3>Cooking Steps</h3>
                          <ol>
                            {selectedMeal.steps.map((step, idx) => (
                              <li key={idx}>{step}</li>
                            ))}
                          </ol>
                        </div>
                      )}
                      {selectedMeal.dietaryTags && (
                        <div className="meal-tags">
                          <h3>Dietary Tags</h3>
                          <div className="tags-container">
                            {selectedMeal.dietaryTags.map((tag, idx) => (
                              <span key={idx} className="dietary-tag">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                      {selectedMeal.mealType && (
                        <div className="meal-types">
                          <h3>Meal Types</h3>
                          <div className="tags-container">
                            {selectedMeal.mealType.map((type, idx) => (
                              <span key={idx} className="meal-type-tag">
                                {type}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Meal Plan Details Modal */}
            {showMealPlanModal && selectedMealPlan && (
              <div className="modal-overlay">
                <div className="modal-content meal-plan-modal">
                  <div className="modal-header">
                    <h2>{selectedMealPlan.name || selectedMealPlan.templateName || 'Meal Plan'}</h2>
                    <button className="close-modal" onClick={closeMealPlanDetails}>
                      <FaTimes />
                    </button>
                  </div>
                  <div className="modal-body">
                    <div className="meal-plan-details">
                      <div className="plan-meta">
                        <div className="meta-row">
                          <FaCalendarAlt />
                          <span>Date: {selectedMealPlan.date ? formatDate(selectedMealPlan.date) : 'Invalid Date'}</span>
                        </div>
                        <div className="meta-row">
                          <FaUtensils />
                          <span>Total Meals: {selectedMealPlan.totalMeals || 0}</span>
                        </div>
                        {selectedMealPlan.totalCalories && (
                          <div className="meta-row">
                            <span>Total Calories: {selectedMealPlan.totalCalories}</span>
                          </div>
                        )}
                        {selectedMealPlan.mealTypes && selectedMealPlan.mealTypes.length > 0 && (
                          <div className="meta-row">
                            <span>Meal Types: {selectedMealPlan.mealTypes.join(', ')}</span>
                          </div>
                        )}
                      </div>

                      {/* Display detailed meals if available */}
                      {loadingMealPlanDetails && (
                        <div className="loading-meals">
                          <p>Loading meal details...</p>
                        </div>
                      )}

                      {mealPlanDetails && mealPlanDetails.meals && mealPlanDetails.meals.length > 0 && (
                        <div className="planned-meals">
                          <h3>Planned Meals</h3>
                          <div className="meals-grid-container">
                            {Object.entries(
                              mealPlanDetails.meals.reduce((acc, meal) => {
                                const date = meal.date;
                                if (!acc[date]) acc[date] = {};
                                if (!acc[date][meal.mealType]) acc[date][meal.mealType] = [];
                                acc[date][meal.mealType].push(meal);
                                return acc;
                              }, {})
                            )
                            .sort(([dateA], [dateB]) => new Date(dateA) - new Date(dateB)) // Sort dates chronologically
                            .map(([date, mealsByType], dayIndex) => (
                              <div key={date} className="day-card-grid">
                                <div className="day-header-grid">
                                  <h4>Day {dayIndex + 1}</h4>
                                  <span className="day-date">{formatDate(date)}</span>
                                </div>
                                <div className="day-meals-grid">
                                  {Object.entries(mealsByType)
                                    .sort(([mealTypeA], [mealTypeB]) => {
                                      // Sort meal types in logical order
                                      const order = { breakfast: 0, lunch: 1, dinner: 2 };
                                      return (order[mealTypeA] || 999) - (order[mealTypeB] || 999);
                                    })
                                    .map(([mealType, meals]) => (
                                      <div key={mealType} className="meal-type-card">
                                        <div className="meal-type-header">
                                          <FaClock className="meal-time-icon" />
                                          <span className="meal-type-name">
                                            {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
                                          </span>
                                        </div>
                                        <div className="meal-items-grid">
                                          {meals.map((meal, idx) => (
                                            <div key={idx} className="meal-item-card">
                                              <div className="meal-name">{meal.mealData?.name || 'Unknown Meal'}</div>
                                              {meal.mealData?.calories && (
                                                <div className="meal-calories">{meal.mealData.calories} cal</div>
                                              )}
                                              {meal.mealData?.category && (
                                                <div className="meal-category">{meal.mealData.category}</div>
                                              )}
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {!loadingMealPlanDetails && !mealPlanDetails && (
                        <div className="favorite-plan-note">
                          <p>This is a favorite meal plan template. Detailed meal information is not available.</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Favorites;
