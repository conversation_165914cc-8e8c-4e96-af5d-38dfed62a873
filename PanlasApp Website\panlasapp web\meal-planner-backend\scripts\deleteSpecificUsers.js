const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

// Users to delete
const USERS_TO_DELETE = [
  '<EMAIL>',
  '<EMAIL>'
];

async function deleteSpecificUsers() {
  try {
    console.log('🗑️  Starting user deletion script...');
    console.log('📧 Target emails:', USERS_TO_DELETE);
    
    // Connect to MongoDB
    console.log('\n📡 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB successfully');

    // Get database info
    const dbName = mongoose.connection.name;
    console.log('📊 Database name:', dbName);

    // Check if users exist before deletion
    console.log('\n🔍 Checking for existing users...');
    for (const email of USERS_TO_DELETE) {
      const user = await User.findOne({ email: email.toLowerCase() });
      if (user) {
        console.log(`✅ Found user: ${email}`);
        console.log(`   - ID: ${user._id}`);
        console.log(`   - Username: ${user.username}`);
        console.log(`   - Name: ${user.firstName} ${user.lastName}`);
        console.log(`   - Created: ${user.createdAt}`);
        console.log(`   - Email Verified: ${user.isEmailVerified}`);
      } else {
        console.log(`❌ User not found: ${email}`);
      }
    }

    // Ask for confirmation
    console.log('\n⚠️  WARNING: This will permanently delete the users listed above!');
    console.log('📝 This action cannot be undone.');
    
    // In a real environment, you might want to add a confirmation prompt
    // For now, we'll proceed with a 3-second delay
    console.log('\n⏳ Proceeding with deletion in 3 seconds...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Delete users
    console.log('\n🗑️  Starting deletion process...');
    let deletedCount = 0;
    let notFoundCount = 0;

    for (const email of USERS_TO_DELETE) {
      try {
        console.log(`\n🔄 Processing: ${email}`);
        
        const result = await User.deleteOne({ email: email.toLowerCase() });
        
        if (result.deletedCount > 0) {
          console.log(`✅ Successfully deleted: ${email}`);
          deletedCount++;
        } else {
          console.log(`❌ User not found: ${email}`);
          notFoundCount++;
        }
      } catch (error) {
        console.error(`❌ Error deleting ${email}:`, error.message);
      }
    }

    // Summary
    console.log('\n📊 DELETION SUMMARY:');
    console.log('='.repeat(50));
    console.log(`✅ Users deleted: ${deletedCount}`);
    console.log(`❌ Users not found: ${notFoundCount}`);
    console.log(`📧 Total processed: ${USERS_TO_DELETE.length}`);

    // Verify deletion
    console.log('\n🔍 Verifying deletion...');
    for (const email of USERS_TO_DELETE) {
      const user = await User.findOne({ email: email.toLowerCase() });
      if (user) {
        console.log(`⚠️  WARNING: User still exists: ${email}`);
      } else {
        console.log(`✅ Confirmed deleted: ${email}`);
      }
    }

    // Get remaining user count
    const totalUsers = await User.countDocuments();
    console.log(`\n📈 Total users remaining in database: ${totalUsers}`);

    console.log('\n🎉 User deletion script completed successfully!');

  } catch (error) {
    console.error('❌ Error in deletion script:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    // Close database connection
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('\n📡 Database connection closed');
    }
    process.exit(0);
  }
}

// Handle script interruption
process.on('SIGINT', async () => {
  console.log('\n\n⚠️  Script interrupted by user');
  if (mongoose.connection.readyState === 1) {
    await mongoose.connection.close();
    console.log('📡 Database connection closed');
  }
  process.exit(0);
});

// Run the script
if (require.main === module) {
  console.log('🚀 Starting User Deletion Script');
  console.log('=' .repeat(50));
  deleteSpecificUsers();
}

module.exports = deleteSpecificUsers;
