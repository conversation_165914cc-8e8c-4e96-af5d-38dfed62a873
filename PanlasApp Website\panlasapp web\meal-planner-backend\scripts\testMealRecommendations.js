const mongoose = require('mongoose');
const geminiService = require('../services/geminiService');
const Meal = require('../models/Meal');
require('dotenv').config();

async function testMealRecommendations() {
  try {
    console.log('🧪 Testing Meal Recommendations from Database...\n');

    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get all meals from database
    const allMeals = await Meal.find();
    console.log(`📊 Found ${allMeals.length} meals in database`);

    // Show sample meal names
    console.log('\n📋 Sample meals in database:');
    allMeals.slice(0, 10).forEach((meal, index) => {
      console.log(`${index + 1}. ${meal.name}`);
    });

    // Test family profile
    const testFamilyProfile = {
      dietaryPreferences: {
        restrictions: ['Vegetarian'],
        allergies: ['Nuts'],
        dislikedIngredients: ['Mushrooms'],
        calorieTarget: 2000,
        mealFrequency: 3
      },
      familyMembers: [
        {
          name: '<PERSON>',
          dateOfBirth: new Date('1985-05-15'),
          dietaryPreferences: {
            restrictions: ['Vegetarian'],
            allergies: ['Nuts'],
            calorieTarget: 1800
          }
        }
      ]
    };

    console.log('\n🔍 Testing AI meal recommendations...');
    const recommendations = await geminiService.generateMealRecommendations(
      testFamilyProfile,
      allMeals,
      'Lose Weight'
    );

    console.log('\n✅ AI Recommendations:');
    console.log(`General Advice: ${recommendations.generalAdvice}`);
    console.log(`\nRecommended Meals (${recommendations.recommendations.length}):`);

    // Verify each recommendation exists in database
    let validRecommendations = 0;
    let invalidRecommendations = 0;

    recommendations.recommendations.forEach((rec, index) => {
      const mealExists = allMeals.some(meal => 
        meal.name.toLowerCase() === rec.mealName.toLowerCase()
      );
      
      if (mealExists) {
        console.log(`✅ ${index + 1}. ${rec.mealName}`);
        console.log(`   Reason: ${rec.reason}`);
        console.log(`   Benefits: ${rec.nutritionalBenefits}`);
        validRecommendations++;
      } else {
        console.log(`❌ ${index + 1}. ${rec.mealName} (NOT FOUND IN DATABASE)`);
        invalidRecommendations++;
      }
    });

    console.log(`\n📊 Validation Results:`);
    console.log(`✅ Valid recommendations: ${validRecommendations}`);
    console.log(`❌ Invalid recommendations: ${invalidRecommendations}`);

    if (invalidRecommendations === 0) {
      console.log('\n🎉 SUCCESS: All recommendations are from the database!');
    } else {
      console.log('\n⚠️  WARNING: Some recommendations are not from the database!');
    }

    // Test chat with meal context
    console.log('\n🗨️  Testing chat with meal context...');
    const chatResponse = await geminiService.generateChatResponse(
      'What are some good vegetarian Filipino breakfast options?',
      { 
        familyProfile: testFamilyProfile,
        availableMeals: allMeals.slice(0, 20) // Provide sample meals
      }
    );

    console.log('Chat Response:', chatResponse);

    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testMealRecommendations();
