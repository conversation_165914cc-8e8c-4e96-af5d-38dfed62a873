const axios = require('axios');

const checkRoutes = async () => {
  try {
    console.log('🔍 Checking available routes...\n');
    
    const response = await axios.get('http://localhost:5000/api/routes');
    const routes = response.data;
    
    console.log('All available routes:');
    console.log('===================');
    
    // Filter admin routes
    const adminRoutes = routes.filter(route => route.path.includes('users'));
    
    console.log('\nAdmin user management routes:');
    adminRoutes.forEach(route => {
      console.log(`${route.methods.join(', ')} ${route.path}`);
    });
    
    // Check specifically for disable/enable routes
    const disableRoutes = routes.filter(route => 
      route.path.includes('disable') || route.path.includes('enable')
    );
    
    console.log('\nDisable/Enable routes:');
    if (disableRoutes.length === 0) {
      console.log('❌ No disable/enable routes found!');
    } else {
      disableRoutes.forEach(route => {
        console.log(`✅ ${route.methods.join(', ')} ${route.path}`);
      });
    }
    
    // Test a specific route
    console.log('\n🧪 Testing specific route patterns...');
    
    const testRoutes = [
      '/api/admin/users/123/disable',
      '/api/admin/users/123/enable',
      '/api/admin/users',
    ];
    
    for (const testRoute of testRoutes) {
      try {
        const testResponse = await axios.put(`http://localhost:5000${testRoute}`, {}, {
          headers: { 'x-auth-token': 'test' }
        });
        console.log(`✅ ${testRoute} - Route exists`);
      } catch (error) {
        if (error.response?.status === 401 || error.response?.status === 403) {
          console.log(`✅ ${testRoute} - Route exists (auth required)`);
        } else if (error.response?.status === 404) {
          console.log(`❌ ${testRoute} - Route not found`);
        } else {
          console.log(`⚠️  ${testRoute} - Status: ${error.response?.status}`);
        }
      }
    }
    
  } catch (error) {
    console.error('Error checking routes:', error.message);
  }
};

checkRoutes();
