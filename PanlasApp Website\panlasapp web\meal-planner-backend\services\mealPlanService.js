import api from './api';

// Get all meal plans for a user
export const getMealPlans = async (userId) => {
  try {
    const response = await api.get('/mealplans', { params: { userId } });
    return response.data;
  } catch (error) {
    console.error('Error fetching meal plans:', error);
    throw error;
  }
};

// Get meal plan for a specific date
export const getMealPlanByDate = async (userId, date) => {
  try {
    const response = await api.get('/mealplans/date', { 
      params: { userId, date } 
    });
    return response.data;
  } catch (error) {
    // If 404, return null instead of throwing error
    if (error.response && error.response.status === 404) {
      return null;
    }
    console.error(`Error fetching meal plan for date ${date}:`, error);
    throw error;
  }
};

// Create or update a meal plan
export const createOrUpdateMealPlan = async (mealPlanData) => {
  try {
    const response = await api.post('/mealplans', mealPlanData);
    return response.data;
  } catch (error) {
    console.error('Error creating/updating meal plan:', error);
    throw error;
  }
};

// Toggle lock status of a meal plan
export const toggleLockMealPlan = async (userId, date, isLocked) => {
  try {
    const response = await api.post('/mealplans/lock', { 
      userId, 
      date, 
      isLocked 
    });
    return response.data;
  } catch (error) {
    console.error(`Error toggling lock status for meal plan on ${date}:`, error);
    throw error;
  }
};

// Mark meal as completed
export const markMealCompleted = async (userId, date, mealType, instanceId, completed) => {
  try {
    const response = await api.post('/mealplans/complete', { 
      userId, 
      date, 
      mealType, 
      instanceId, 
      completed 
    });
    return response.data;
  } catch (error) {
    console.error(`Error marking meal as ${completed ? 'completed' : 'incomplete'}:`, error);
    throw error;
  }
};

// Delete a meal plan
export const deleteMealPlan = async (userId, date) => {
  try {
    const response = await api.delete('/mealplans', { 
      params: { userId, date } 
    });
    return response.data;
  } catch (error) {
    console.error(`Error deleting meal plan for date ${date}:`, error);
    throw error;
  }
};
