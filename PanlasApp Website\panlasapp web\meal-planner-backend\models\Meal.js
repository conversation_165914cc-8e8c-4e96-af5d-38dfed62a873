const mongoose = require('mongoose');

const MealSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  calories: {
    type: Number,
    required: true,
    min: 0
  },
  protein: {
    type: Number,
    required: true,
    min: 0
  },
  carbs: {
    type: Number,
    required: true,
    min: 0
  },
  fat: {
    type: Number,
    required: true,
    min: 0
  },
  category: [{
    type: String,
    trim: true,
    required: true
  }],
  mealType: [{
    type: String,
    trim: true
  }],
  image: {
    type: String
  },
  ingredients: [{
    type: String,
    trim: true
  }],
  instructions: [{
    type: String,
    trim: true
  }],
  dietaryTags: [{
    type: String,
    trim: true
  }],
  allergens: [{
    type: String,
    trim: true
  }],
  rating: {
    type: Number,
    min: 0,
    max: 5,
    default: 0
  },
  prepTime: {
    type: Number,
    min: 0
  },
  price: {
    type: Number,
    min: 0,
    default: 0
  },
  region: {
    type: String,
    trim: true,
    default: 'Philippines'
  },
  servingSize: {
    type: Number,
    default: 4,
    min: 1
  },
  dietaryAttributes: {
    isVegetarian: {
      type: Boolean,
      default: false
    },
    isVegan: {
      type: Boolean,
      default: false
    },
    isGlutenFree: {
      type: Boolean,
      default: false
    },
    isDairyFree: {
      type: Boolean,
      default: false
    },
    isNutFree: {
      type: Boolean,
      default: false
    },
    isLowCarb: {
      type: Boolean,
      default: false
    }
  },
  dietType: {
    isVegetarian: {
      type: Boolean,
      default: false
    },
    isVegan: {
      type: Boolean,
      default: false
    },
    isGlutenFree: {
      type: Boolean,
      default: false
    },
    isDairyFree: {
      type: Boolean,
      default: false
    },
    isNutFree: {
      type: Boolean,
      default: false
    },
    isLowCarb: {
      type: Boolean,
      default: false
    },
    isKeto: {
      type: Boolean,
      default: false
    },
    isPescatarian: {
      type: Boolean,
      default: false
    },
    isHalal: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true
});

// Add text index for search functionality
MealSchema.index({
  name: 'text',
  description: 'text',
  dietaryTags: 'text',
  allergens: 'text',
  'ingredients': 'text'
});

const Meal = mongoose.model('Meal', MealSchema);
module.exports = Meal;
