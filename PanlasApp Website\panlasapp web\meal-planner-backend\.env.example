# Database Configuration
MONGODB_URI=mongodb://localhost:27017/meal_planner

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRATION=24h

# Server Configuration
PORT=5000
NODE_ENV=development

# Frontend URL (for email links)
FRONTEND_URL=http://localhost:3000

# Email Configuration (Gmail SMTP)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password-here

# Email Configuration (Alternative - SendGrid)
# SENDGRID_API_KEY=your-sendgrid-api-key
# SENDGRID_FROM_EMAIL=<EMAIL>

# Email Configuration (Alternative - Mailgun)
# MAILGUN_API_KEY=your-mailgun-api-key
# MAILGUN_DOMAIN=your-mailgun-domain

# Rate Limiting Configuration
MAX_LOGIN_ATTEMPTS=5
LOGIN_WINDOW_MS=900000
BLOCK_DURATION_MS=3600000

# Security Configuration
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=6

# API Configuration
API_BASE_URL=http://localhost:5000/api

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Session Configuration
SESSION_SECRET=your_session_secret_here

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Third-party API Keys (if needed)
NUTRITION_API_KEY=your_nutrition_api_key
RECIPE_API_KEY=your_recipe_api_key
