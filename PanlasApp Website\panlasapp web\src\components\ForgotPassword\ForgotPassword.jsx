import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import EmailValidator from '../EmailValidator/EmailValidator';
import '../../styles/Auth.css';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [error, setError] = useState('');
  const [showEmailValidation, setShowEmailValidation] = useState(false);
  const navigate = useNavigate();

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (e) => {
    const value = e.target.value;
    setEmail(value);
    setShowEmailValidation(value.length > 0);
    
    // Clear error when user types
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setError('Please enter your email address');
      return;
    }

    if (!validateEmail(email.trim())) {
      setError('Please enter a valid email address');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await axios.post('http://localhost:5000/api/password-reset/forgot', {
        email: email.toLowerCase().trim()
      });

      if (response.data.success) {
        setEmailSent(true);
      } else {
        setError(response.data.message || 'Failed to send reset email');
      }
    } catch (err) {
      if (err.response) {
        setError(err.response.data.message || 'Failed to send reset email. Please try again.');
      } else if (err.request) {
        setError('Server not responding. Please try again later.');
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
      console.error('Forgot password error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  const handleContinueToReset = () => {
    navigate('/reset-password', { state: { email: email.toLowerCase().trim() } });
  };

  if (emailSent) {
    return (
      <div className="auth-container">
        <div className="auth-box">
          <button className="otp-back-button" onClick={handleBackToLogin}>
            ←
          </button>
          
          <div className="otp-container">
            <div className="otp-header">
              <div className="otp-icon">✅</div>
              <h2 className="otp-title">Email Sent!</h2>
              <p className="otp-subtitle">
                If an account with that email exists, we've sent you a password reset token.
              </p>
              <p className="otp-email">{email}</p>
              <p className="otp-subtitle" style={{ marginTop: '10px', fontSize: '14px' }}>
                Check your email and follow the instructions to reset your password.
              </p>
            </div>

            <button
              className="otp-verify-button"
              onClick={handleContinueToReset}
            >
              Continue to Reset
            </button>

            <div className="otp-resend-container">
              <button
                className="otp-resend-button"
                onClick={handleBackToLogin}
              >
                Back to Login
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-container">
      <div className="auth-box">
        <button className="otp-back-button" onClick={handleBackToLogin}>
          ←
        </button>
        
        <div className="heading">Forgot Password?</div>
        
        <div className="otp-container">
          <div className="otp-header">
            <div className="otp-icon">🔒</div>
            <h2 className="otp-title">Reset Your Password</h2>
            <p className="otp-subtitle">
              Enter your email address and we'll send you a reset token to create a new password.
            </p>
          </div>

          {error && <div className="error-message">{error}</div>}

          <form className="form" onSubmit={handleSubmit}>
            <div className="input-group full-width">
              <input
                placeholder="Enter your email address"
                id="email"
                name="email"
                type="email"
                className={`input ${error ? 'error' : ''}`}
                value={email}
                onChange={handleEmailChange}
                autoCapitalize="none"
                autoCorrect={false}
                disabled={loading}
              />
              <EmailValidator
                email={email}
                showValidation={showEmailValidation}
              />
            </div>

            <button
              type="submit"
              className="auth-button"
              disabled={loading || !validateEmail(email)}
            >
              {loading ? 'Sending...' : 'Send Reset Token'}
            </button>
          </form>

          <div className="auth-link">
            <span>Remember your password? </span>
            <Link to="/login">Sign In</Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
