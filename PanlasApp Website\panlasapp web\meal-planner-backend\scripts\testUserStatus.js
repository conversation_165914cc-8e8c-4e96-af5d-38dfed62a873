const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster');
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

// Test user status
const testUserStatus = async () => {
  try {
    console.log('Testing user status...\n');
    
    // Get all users and their status
    const users = await User.find({}, 'username email isActive disabledAt disabledBy').lean();
    
    console.log('All Users Status:');
    console.log('=================');
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.username} (${user.email})`);
      console.log(`   isActive: ${user.isActive}`);
      console.log(`   disabledAt: ${user.disabledAt || 'N/A'}`);
      console.log(`   disabledBy: ${user.disabledBy || 'N/A'}`);
      console.log('');
    });
    
    // Summary
    const totalUsers = users.length;
    const activeUsers = users.filter(user => user.isActive === true).length;
    const inactiveUsers = users.filter(user => user.isActive === false).length;
    const undefinedUsers = users.filter(user => user.isActive === undefined || user.isActive === null).length;
    
    console.log('Summary:');
    console.log('========');
    console.log(`Total Users: ${totalUsers}`);
    console.log(`Active Users: ${activeUsers}`);
    console.log(`Inactive Users: ${inactiveUsers}`);
    console.log(`Undefined Status: ${undefinedUsers}`);
    
  } catch (error) {
    console.error('Error testing user status:', error);
  }
};

// Main function
const main = async () => {
  await connectDB();
  await testUserStatus();
  await mongoose.connection.close();
  console.log('Database connection closed.');
  process.exit(0);
};

// Run the script
main();
