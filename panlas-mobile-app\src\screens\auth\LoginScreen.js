import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../context/AuthContext';
import { colors, legacyFonts, legacySpacing, borderRadius } from '../../styles/theme';

const LoginScreen = ({ navigation, route }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [loading, setLoading] = useState(false);

  const { login } = useAuth();

  // Pre-fill email if coming from registration
  useEffect(() => {
    if (route.params?.prefillEmail) {
      setFormData(prev => ({
        ...prev,
        email: route.params.prefillEmail
      }));
    }
  }, [route.params]);

  const handleLogin = async () => {
    if (!formData.email || !formData.password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    try {
      const result = await login({
        email: formData.email.trim(),
        password: formData.password,
      });

      if (!result.success) {
        Alert.alert('Login Failed', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Sign In</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Content */}
      <View style={styles.content}>
        <View style={styles.welcomeSection}>
          <Text style={styles.title}>Welcome Back!</Text>
          <Text style={styles.subtitle}>
            {route.params?.prefillEmail
              ? 'We found an existing account with this email'
              : 'Sign in to continue to Panlas'
            }
          </Text>
        </View>

        <View style={styles.formSection}>
          {/* Email Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your email"
              value={formData.email}
              onChangeText={(text) => setFormData({...formData, email: text})}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          {/* Password Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Password</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your password"
              value={formData.password}
              onChangeText={(text) => setFormData({...formData, password: text})}
              secureTextEntry
              autoCapitalize="none"
            />
          </View>

          {/* Forgot Password Link */}
          <TouchableOpacity
            style={styles.forgotPasswordContainer}
            onPress={() => navigation.navigate('ForgotPassword')}
          >
            <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
          </TouchableOpacity>

          {/* Login Button */}
          <TouchableOpacity
            style={[styles.loginButton, loading && styles.disabledButton]}
            onPress={handleLogin}
            disabled={loading}
          >
            <Text style={styles.loginButtonText}>
              {loading ? 'Signing In...' : 'Sign In'}
            </Text>
          </TouchableOpacity>

          {/* Forgot Password Link */}
          <TouchableOpacity
            style={styles.forgotPasswordButton}
            onPress={() => navigation.navigate('ForgotPassword')}
          >
            <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
          </TouchableOpacity>

          {/* Register Link */}
          <View style={styles.registerSection}>
            <Text style={styles.registerText}>Don't have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Register')}>
              <Text style={styles.registerLink}>Create Account</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: legacySpacing.sm,
  },
  headerTitle: {
    fontSize: legacyFonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: legacySpacing.lg,
  },
  welcomeSection: {
    alignItems: 'center',
    marginBottom: legacySpacing.xl,
    marginTop: legacySpacing.lg,
  },
  title: {
    fontSize: legacyFonts.sizes.xxlarge,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: legacySpacing.sm,
  },
  subtitle: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  formSection: {
    flex: 1,
  },
  inputContainer: {
    marginBottom: legacySpacing.lg,
  },
  inputLabel: {
    fontSize: legacyFonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    marginBottom: legacySpacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    fontSize: legacyFonts.sizes.medium,
    backgroundColor: colors.surface,
  },
  loginButton: {
    backgroundColor: colors.primary,
    paddingVertical: legacySpacing.md,
    borderRadius: borderRadius.medium,
    alignItems: 'center',
    marginTop: legacySpacing.lg,
  },
  loginButtonText: {
    color: colors.surface,
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
  forgotPasswordButton: {
    alignItems: 'center',
    marginTop: legacySpacing.md,
    marginBottom: legacySpacing.sm,
  },
  forgotPasswordText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  registerSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: legacySpacing.lg,
  },
  registerText: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
  },
  registerLink: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.primary,
    fontWeight: 'bold',
  },
  forgotPasswordContainer: {
    alignItems: 'flex-end',
    marginBottom: legacySpacing.sm,
  },
  forgotPasswordText: {
    fontSize: legacyFonts.sizes.small,
    color: colors.primary,
    fontWeight: '500',
  },
});

export default LoginScreen;
