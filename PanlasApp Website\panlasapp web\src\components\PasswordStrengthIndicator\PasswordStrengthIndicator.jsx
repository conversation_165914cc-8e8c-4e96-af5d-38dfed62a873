import React from 'react';

const PasswordStrengthIndicator = ({ password }) => {
  const getPasswordStrength = (password) => {
    if (!password) return { strength: 'none', score: 0, label: '', color: '#E0E0E0' };
    
    let score = 0;
    let feedback = [];
    
    // Length check
    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('At least 8 characters');
    }
    
    // Uppercase check
    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('One uppercase letter');
    }
    
    // Lowercase check
    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('One lowercase letter');
    }
    
    // Number check
    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('One number');
    }
    
    // Special character check
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    } else {
      feedback.push('One special character');
    }
    
    // Determine strength
    let strength, label, color;
    if (score <= 2) {
      strength = 'weak';
      label = 'Weak';
      color = '#FF5252';
    } else if (score <= 3) {
      strength = 'medium';
      label = 'Medium';
      color = '#FF9800';
    } else {
      strength = 'strong';
      label = 'Strong';
      color = '#4CAF50';
    }
    
    return { strength, score, label, color, feedback };
  };

  const passwordInfo = getPasswordStrength(password);
  
  if (!password) return null;

  return (
    <div className="password-strength-indicator">
      <div className="password-strength-header">
        <span className="password-strength-label">Password Strength:</span>
        <span className={`password-strength-value ${passwordInfo.strength}`}>
          {passwordInfo.label}
        </span>
      </div>
      
      <div className="password-progress-container">
        <div className="password-progress-bar">
          <div 
            className="password-progress-fill"
            style={{ 
              width: `${(passwordInfo.score / 5) * 100}%`,
              backgroundColor: passwordInfo.color 
            }} 
          />
        </div>
      </div>
      
      {passwordInfo.feedback.length > 0 && (
        <div className="password-feedback">
          <div className="password-feedback-title">Missing:</div>
          {passwordInfo.feedback.map((item, index) => (
            <div key={index} className="password-feedback-item">• {item}</div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PasswordStrengthIndicator;
