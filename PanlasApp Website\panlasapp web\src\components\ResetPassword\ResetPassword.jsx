import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import PasswordStrengthIndicator from '../PasswordStrengthIndicator/PasswordStrengthIndicator';
import '../../styles/Auth.css';

const ResetPassword = () => {
  const [step, setStep] = useState(1); // 1: Enter token, 2: Enter new password
  const [resetToken, setResetToken] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPasswordStrength, setShowPasswordStrength] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get email from navigation state
  const email = location.state?.email || '';

  useEffect(() => {
    // If no email provided, redirect to forgot password
    if (!email) {
      navigate('/forgot-password');
    }
  }, [email, navigate]);

  const validatePassword = (password) => {
    return password.length >= 8 &&
           /[A-Z]/.test(password) &&
           /[a-z]/.test(password) &&
           /\d/.test(password) &&
           /[!@#$%^&*(),.?":{}|<>]/.test(password);
  };

  const handleTokenChange = (e) => {
    setResetToken(e.target.value);
    if (error) setError('');
  };

  const handlePasswordChange = (e) => {
    const value = e.target.value;
    setNewPassword(value);
    setShowPasswordStrength(value.length > 0);
    if (error) setError('');
  };

  const handleConfirmPasswordChange = (e) => {
    setConfirmPassword(e.target.value);
    if (error) setError('');
  };

  const handleVerifyToken = async (e) => {
    e.preventDefault();
    
    if (!resetToken.trim()) {
      setError('Please enter the reset token');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await axios.post('http://localhost:5000/api/password-reset/verify-token', {
        token: resetToken.trim()
      });

      if (response.data.success) {
        setStep(2);
      } else {
        setError(response.data.message || 'Invalid or expired token');
      }
    } catch (err) {
      if (err.response) {
        setError(err.response.data.message || 'Invalid or expired token');
      } else {
        setError('Network error. Please check your connection and try again.');
      }
      console.error('Token verification error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async (e) => {
    e.preventDefault();
    
    if (!newPassword || !confirmPassword) {
      setError('Please fill in all fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (!validatePassword(newPassword)) {
      setError('Password must be at least 8 characters long and contain uppercase, lowercase, numbers, and special characters.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await axios.post('http://localhost:5000/api/password-reset/reset', {
        token: resetToken.trim(),
        newPassword: newPassword
      });

      if (response.data.success) {
        // Show success message and redirect to login
        alert('Success! Your password has been reset successfully. You can now login with your new password.');
        navigate('/login');
      } else {
        setError(response.data.message || 'Failed to reset password');
      }
    } catch (err) {
      if (err.response) {
        setError(err.response.data.message || 'Failed to reset password');
      } else {
        setError('Network error. Please check your connection and try again.');
      }
      console.error('Password reset error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  const handleBackToForgotPassword = () => {
    navigate('/forgot-password');
  };

  return (
    <div className="auth-container">
      <div className="auth-box">
        <button className="otp-back-button" onClick={step === 1 ? handleBackToForgotPassword : handleBackToLogin}>
          ←
        </button>
        
        <div className="heading">Reset Password</div>
        
        <div className="otp-container">
          <div className="otp-header">
            <div className="otp-icon">{step === 1 ? '🔑' : '🔒'}</div>
            <h2 className="otp-title">
              {step === 1 ? 'Enter Reset Token' : 'Create New Password'}
            </h2>
            <p className="otp-subtitle">
              {step === 1 
                ? 'Enter the reset token sent to your email address:'
                : 'Enter your new password below:'
              }
            </p>
            {step === 1 && <p className="otp-email">{email}</p>}
          </div>

          {error && <div className="error-message">{error}</div>}

          {step === 1 ? (
            <form className="form" onSubmit={handleVerifyToken}>
              <div className="input-group full-width">
                <input
                  placeholder="Enter reset token"
                  id="resetToken"
                  name="resetToken"
                  type="text"
                  className={`input ${error ? 'error' : ''}`}
                  value={resetToken}
                  onChange={handleTokenChange}
                  disabled={loading}
                />
              </div>

              <button
                type="submit"
                className="auth-button"
                disabled={loading || !resetToken.trim()}
              >
                {loading ? 'Verifying...' : 'Verify Token'}
              </button>
            </form>
          ) : (
            <form className="form" onSubmit={handleResetPassword}>
              <div className="input-group full-width">
                <input
                  placeholder="New Password"
                  id="newPassword"
                  name="newPassword"
                  type="password"
                  className={`input ${error ? 'error' : ''}`}
                  value={newPassword}
                  onChange={handlePasswordChange}
                  disabled={loading}
                />
                <PasswordStrengthIndicator password={newPassword} />
              </div>

              <div className="input-group full-width">
                <input
                  placeholder="Confirm New Password"
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  className={`input ${error ? 'error' : ''}`}
                  value={confirmPassword}
                  onChange={handleConfirmPasswordChange}
                  disabled={loading}
                />
              </div>

              <button
                type="submit"
                className="auth-button"
                disabled={loading || !newPassword || !confirmPassword || newPassword !== confirmPassword}
              >
                {loading ? 'Resetting...' : 'Reset Password'}
              </button>
            </form>
          )}

          <div className="auth-link">
            <span>Remember your password? </span>
            <Link to="/login">Sign In</Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
