import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import apiService from '../../../meal-planner-backend/services/apiService';
import { useAdminView } from '../../context/AdminViewContext';

const AdminRoute = ({ children }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { isViewingAsUser } = useAdminView();
  
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        // First check if user is authenticated at all
        if (!apiService.isAuthenticated()) {
          setIsLoading(false);
          return;
        }
        
        // Get user profile to check admin status
        const userProfile = await apiService.getUserProfile();
        setIsAdmin(userProfile.isAdmin === true);
        setIsLoading(false);
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsLoading(false);
      }
    };
    
    checkAdminStatus();
  }, []);
  
  // Show loading state while checking admin status
  if (isLoading) {
    return <div>Checking permissions...</div>;
  }
  
  // If not authenticated or not an admin, redirect to home
  if (!apiService.isAuthenticated() || !isAdmin) {
    console.log('User not authorized to access admin area');
    return <Navigate to="/home" replace />;
  }

  // If admin is viewing as user, redirect to home
  if (isViewingAsUser) {
    console.log('Admin is viewing as user, redirecting to home');
    return <Navigate to="/home" replace />;
  }

  // If admin and not viewing as user, render the protected component
  return children;
};

export default AdminRoute;
