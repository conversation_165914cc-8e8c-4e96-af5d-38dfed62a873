import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFavorites } from '../../context/FavoritesContext';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const FavoritesScreen = ({ navigation }) => {
  const {
    favorites,
    favoriteMealPlans,
    loading,
    loadFavorites,
    removeFavorite,
    addFavoriteMealPlan,
    removeFavoriteMealPlan,
    loadFavoriteMealPlans
  } = useFavorites();

  const [activeTab, setActiveTab] = useState('meals'); // 'meals' or 'plans'

  const handleMealPress = (meal) => {
    navigation.navigate('Home', {
      screen: 'MealDetail',
      params: { meal }
    });
  };

  const handleRemoveFavorite = async (mealId) => {
    await removeFavorite(mealId);
  };

  const handleRemoveFavoriteMealPlan = async (planId) => {
    await removeFavoriteMealPlan(planId);
  };

  const handleMealPlanPress = (mealPlan) => {
    // Navigate to meal plan detail or meal plans screen
    navigation.navigate('MealPlans');
  };

  const renderFavoriteMealPlan = ({ item: mealPlan }) => (
    <TouchableOpacity
      style={[commonStyles.foodCard, styles.mealPlanCard]}
      onPress={() => handleMealPlanPress(mealPlan)}
    >
      <View style={styles.mealPlanHeader}>
        <View style={styles.mealPlanIcon}>
          <Ionicons name="calendar-outline" size={24} color={colors.primary} />
        </View>
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveFavoriteMealPlan(mealPlan._id || mealPlan.id)}
        >
          <Ionicons name="heart" size={24} color={colors.secondary} />
        </TouchableOpacity>
      </View>
      <View style={commonStyles.foodCardContent}>
        <Text style={commonStyles.foodCardTitle} numberOfLines={2}>
          {mealPlan.name || 'Meal Plan'}
        </Text>
        <View style={commonStyles.foodCardMeta}>
          <View style={styles.mealPlanMeta}>
            <Text style={styles.mealPlanDate}>
              {mealPlan.date || 'No date'}
            </Text>
            {mealPlan.totalMeals > 0 && (
              <Text style={styles.mealPlanMeals}>
                {mealPlan.totalMeals} meals
              </Text>
            )}
            {mealPlan.totalCalories > 0 && (
              <Text style={styles.mealPlanCalories}>
                {mealPlan.totalCalories} cal
              </Text>
            )}
          </View>
        </View>
        {mealPlan.mealTypes && mealPlan.mealTypes.length > 0 && (
          <View style={styles.mealTypesContainer}>
            {mealPlan.mealTypes.map((type, index) => (
              <View key={index} style={styles.mealTypeTag}>
                <Text style={styles.mealTypeText}>{type}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderFavoriteItem = ({ item: meal }) => (
    <TouchableOpacity
      style={commonStyles.foodCard}
      onPress={() => handleMealPress(meal)}
    >
      <View style={commonStyles.foodCardImage}>
        <Image
          source={{ uri: meal.image || 'https://via.placeholder.com/300x200' }}
          style={styles.mealImage}
          resizeMode="cover"
        />
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveFavorite(meal.id || meal._id)}
        >
          <Ionicons name="heart" size={24} color={colors.secondary} />
        </TouchableOpacity>
      </View>
      <View style={commonStyles.foodCardContent}>
        <Text style={commonStyles.foodCardTitle} numberOfLines={2}>
          {meal.name}
        </Text>
        <View style={commonStyles.foodCardMeta}>
          <View style={commonStyles.categoryTag}>
            <Text style={commonStyles.categoryTagText}>
              {Array.isArray(meal.category) ? meal.category[0] : meal.category}
            </Text>
          </View>
          <View style={commonStyles.rating}>
            <Ionicons name="star" size={16} color="#FFD700" />
            <Text style={commonStyles.ratingText}>
              {meal.rating || '4.5'}
            </Text>
          </View>
        </View>
        {meal.description && (
          <Text style={styles.mealDescription} numberOfLines={2}>
            {meal.description}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Favorites</Text>
        <Text style={styles.headerSubtitle}>
          {activeTab === 'meals'
            ? `${favorites.length} saved meal${favorites.length !== 1 ? 's' : ''}`
            : `${favoriteMealPlans.length} saved plan${favoriteMealPlans.length !== 1 ? 's' : ''}`
          }
        </Text>
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'meals' && styles.tabButtonActive]}
          onPress={() => setActiveTab('meals')}
        >
          <Ionicons
            name="restaurant-outline"
            size={20}
            color={activeTab === 'meals' ? colors.surface : colors.textSecondary}
          />
          <Text style={[
            styles.tabButtonText,
            activeTab === 'meals' && styles.tabButtonTextActive
          ]}>
            Meals
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'plans' && styles.tabButtonActive]}
          onPress={() => setActiveTab('plans')}
        >
          <Ionicons
            name="calendar-outline"
            size={20}
            color={activeTab === 'plans' ? colors.surface : colors.textSecondary}
          />
          <Text style={[
            styles.tabButtonText,
            activeTab === 'plans' && styles.tabButtonTextActive
          ]}>
            Meal Plans
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {activeTab === 'meals' ? (
        <FlatList
          data={favorites}
          renderItem={renderFavoriteItem}
          keyExtractor={(item) => (item.id || item._id).toString()}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={loadFavorites}
              colors={[colors.primary]}
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="heart-outline" size={64} color={colors.textSecondary} />
              <Text style={styles.emptyTitle}>No favorite meals yet</Text>
              <Text style={styles.emptySubtitle}>
                Start adding meals to your favorites to see them here
              </Text>
              <TouchableOpacity
                style={styles.browseButton}
                onPress={() => navigation.navigate('Home')}
              >
                <Text style={styles.browseButtonText}>Browse Meals</Text>
              </TouchableOpacity>
            </View>
          }
        />
      ) : (
        <FlatList
          data={favoriteMealPlans}
          renderItem={renderFavoriteMealPlan}
          keyExtractor={(item) => (item._id || item.id).toString()}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={loadFavoriteMealPlans}
              colors={[colors.primary]}
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="calendar-outline" size={64} color={colors.textSecondary} />
              <Text style={styles.emptyTitle}>No favorite meal plans yet</Text>
              <Text style={styles.emptySubtitle}>
                Start adding meal plans to your favorites to see them here
              </Text>
              <TouchableOpacity
                style={styles.browseButton}
                onPress={() => navigation.navigate('MealPlans')}
              >
                <Text style={styles.browseButtonText}>Browse Meal Plans</Text>
              </TouchableOpacity>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  headerTitle: {
    fontSize: fonts.sizes.xlarge,
    fontWeight: 'bold',
    color: colors.surface,
  },
  headerSubtitle: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    opacity: 0.8,
    marginTop: spacing.xs,
  },
  listContainer: {
    padding: spacing.md,
  },
  mealImage: {
    width: '100%',
    height: 200,
  },
  removeButton: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: borderRadius.round,
    padding: spacing.sm,
  },
  mealDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    lineHeight: 18,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxl,
    paddingHorizontal: spacing.lg,
  },
  emptyTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  emptySubtitle: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginTop: spacing.sm,
    textAlign: 'center',
    lineHeight: 22,
  },
  browseButton: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.medium,
    marginTop: spacing.lg,
  },
  browseButtonText: {
    color: colors.surface,
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    marginHorizontal: spacing.md,
    marginTop: spacing.md,
    borderRadius: borderRadius.medium,
    padding: spacing.xs,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.small,
  },
  tabButtonActive: {
    backgroundColor: colors.primary,
  },
  tabButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  tabButtonTextActive: {
    color: colors.surface,
    fontWeight: 'bold',
  },
  mealPlanCard: {
    marginBottom: spacing.md,
  },
  mealPlanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.surface,
    borderTopLeftRadius: borderRadius.medium,
    borderTopRightRadius: borderRadius.medium,
  },
  mealPlanIcon: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.medium,
    backgroundColor: colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  mealPlanMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  mealPlanDate: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    backgroundColor: colors.background,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
  },
  mealPlanMeals: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    fontWeight: '500',
  },
  mealPlanCalories: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  mealTypesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: spacing.sm,
    gap: spacing.xs,
  },
  mealTypeTag: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
  },
  mealTypeText: {
    fontSize: fonts.sizes.small,
    color: colors.surface,
    fontWeight: '500',
  },
});

export default FavoritesScreen;
