const mongoose = require('mongoose');
const Meal = require('../models/Meal');
require('dotenv').config();

// High-quality image URLs for Filipino dishes
const filipinoMealImages = {
  // Meat Dishes
  'Adobong Manok': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  'Adobong Baboy': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  'Beef Caldereta': 'https://images.unsplash.com/photo-1574484284002-952d92456975?w=800&h=600&fit=crop',
  'Beef Nilaga': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=800&h=600&fit=crop',
  'Beef Pares': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=800&h=600&fit=crop',
  'Beef Tapa': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  'Bicol Express': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  'Chicken Curry': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  'Chicken Inasal': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  'Crispy Pata': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  'Kare-Kare': 'https://images.unsplash.com/photo-1574484284002-952d92456975?w=800&h=600&fit=crop',
  'Lechon Kawali': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  'Longganisa': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  'Mechado': 'https://images.unsplash.com/photo-1574484284002-952d92456975?w=800&h=600&fit=crop',
  'Morcon': 'https://images.unsplash.com/photo-1574484284002-952d92456975?w=800&h=600&fit=crop',
  'Sisig': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  'Tocino': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',

  // Seafood
  'Bangus Sisig': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=800&h=600&fit=crop',
  'Fish Escabeche': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=800&h=600&fit=crop',
  'Grilled Bangus': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=800&h=600&fit=crop',
  'Grilled Tilapia': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=800&h=600&fit=crop',
  'Lapu-Lapu Escabeche': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=800&h=600&fit=crop',
  'Rellenong Bangus': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=800&h=600&fit=crop',
  'Sweet and Sour Fish': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=800&h=600&fit=crop',

  // Soups
  'Bulalo': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=800&h=600&fit=crop',
  'Chicken Arroz Caldo': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=800&h=600&fit=crop',
  'Chicken Tinola': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=800&h=600&fit=crop',
  'Molo Soup': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=800&h=600&fit=crop',
  'Sinigang na Baboy': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=800&h=600&fit=crop',
  'Sinigang na Hipon': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=800&h=600&fit=crop',

  // Vegetables
  'Adobong Kangkong': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=800&h=600&fit=crop',
  'Ginataang Gulay': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=800&h=600&fit=crop',
  'Laing': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=800&h=600&fit=crop',
  'Pinakbet': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=800&h=600&fit=crop',

  // Noodles
  'Pancit Canton': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=800&h=600&fit=crop',
  'Pancit Bihon': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=800&h=600&fit=crop',
  'Pancit Malabon': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=800&h=600&fit=crop',
  'Pancit Palabok': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=800&h=600&fit=crop',

  // Rice Dishes
  'Chicken Biryani': 'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=800&h=600&fit=crop',
  'Garlic Rice': 'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=800&h=600&fit=crop',
  'Java Rice': 'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=800&h=600&fit=crop',
  'Sinangag': 'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=800&h=600&fit=crop',

  // Desserts
  'Bibingka': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&h=600&fit=crop',
  'Buko Pie': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&h=600&fit=crop',
  'Champorado': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&h=600&fit=crop',
  'Halo-Halo': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&h=600&fit=crop',
  'Leche Flan': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&h=600&fit=crop',
  'Maja Blanca': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&h=600&fit=crop',
  'Taho': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&h=600&fit=crop',
  'Turon': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&h=600&fit=crop',

  // Default fallback images by category
  'default_meat': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  'default_seafood': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=800&h=600&fit=crop',
  'default_vegetable': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=800&h=600&fit=crop',
  'default_soup': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=800&h=600&fit=crop',
  'default_noodles': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=800&h=600&fit=crop',
  'default_rice': 'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=800&h=600&fit=crop',
  'default_dessert': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&h=600&fit=crop',
  'default_filipino': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop'
};

// Function to get appropriate image URL for a meal
function getImageForMeal(meal) {
  // First, try to find exact match by name
  if (filipinoMealImages[meal.name]) {
    return filipinoMealImages[meal.name];
  }

  // If no exact match, try to match by category
  const category = Array.isArray(meal.category) ? meal.category[0] : meal.category;
  
  if (category) {
    const categoryLower = category.toLowerCase();
    
    if (categoryLower.includes('meat') || categoryLower.includes('chicken') || categoryLower.includes('beef') || categoryLower.includes('pork')) {
      return filipinoMealImages['default_meat'];
    } else if (categoryLower.includes('seafood') || categoryLower.includes('fish')) {
      return filipinoMealImages['default_seafood'];
    } else if (categoryLower.includes('vegetable') || categoryLower.includes('vegan')) {
      return filipinoMealImages['default_vegetable'];
    } else if (categoryLower.includes('soup')) {
      return filipinoMealImages['default_soup'];
    } else if (categoryLower.includes('noodle') || categoryLower.includes('pancit')) {
      return filipinoMealImages['default_noodles'];
    } else if (categoryLower.includes('rice')) {
      return filipinoMealImages['default_rice'];
    } else if (categoryLower.includes('dessert') || categoryLower.includes('sweet')) {
      return filipinoMealImages['default_dessert'];
    }
  }

  // Default fallback
  return filipinoMealImages['default_filipino'];
}

async function updateMealImages() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    console.log('📊 Fetching all meals...');
    const meals = await Meal.find({});
    console.log(`Found ${meals.length} meals to update`);

    let updatedCount = 0;
    let skippedCount = 0;

    for (const meal of meals) {
      const currentImage = meal.image;
      
      // Check if image needs updating (broken local URLs or missing)
      const needsUpdate = !currentImage || 
                         currentImage.includes('*************') || 
                         currentImage.startsWith('/imagesfood/') ||
                         currentImage.includes('placeholder');

      if (needsUpdate) {
        const newImageUrl = getImageForMeal(meal);
        
        await Meal.findByIdAndUpdate(meal._id, {
          image: newImageUrl
        });
        
        console.log(`✅ Updated "${meal.name}": ${newImageUrl}`);
        updatedCount++;
      } else {
        console.log(`⏭️  Skipped "${meal.name}": Already has valid image`);
        skippedCount++;
      }
    }

    console.log('\n🎉 Update completed!');
    console.log(`✅ Updated: ${updatedCount} meals`);
    console.log(`⏭️  Skipped: ${skippedCount} meals`);
    console.log(`📊 Total: ${meals.length} meals`);

  } catch (error) {
    console.error('❌ Error updating meal images:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  updateMealImages();
}

module.exports = { updateMealImages, getImageForMeal };
