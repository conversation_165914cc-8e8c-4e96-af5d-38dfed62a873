const axios = require('axios');
const mongoose = require('mongoose');
const User = require('../models/User');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function testAIEndpoints() {
  try {
    console.log('🧪 Testing AI API Endpoints...\n');

    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Create or find a test user
    let testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      testUser = new User({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashedpassword',
        dietaryPreferences: {
          restrictions: ['Vegetarian'],
          allergies: ['Nuts'],
          dislikedIngredients: ['Mushrooms'],
          calorieTarget: 2000,
          mealFrequency: 3
        }
      });
      await testUser.save();
      console.log('✅ Created test user');
    } else {
      console.log('✅ Found existing test user');
    }

    // Generate JWT token
    const token = jwt.sign(
      { id: testUser._id, email: testUser.email },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: '1h' }
    );

    const baseURL = 'http://localhost:5000/api/ai';
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };

    // Test 1: Get goals and health conditions
    console.log('\n1. Testing GET /goals...');
    try {
      const goalsResponse = await axios.get(`${baseURL}/goals`);
      console.log('✅ Goals endpoint successful');
      console.log(`Found ${goalsResponse.data.goals.length} goals and ${goalsResponse.data.healthConditions.length} health conditions`);
    } catch (error) {
      console.error('❌ Goals endpoint failed:', error.response?.data || error.message);
    }

    // Test 2: Dietary conflicts detection
    console.log('\n2. Testing POST /dietary-conflicts...');
    try {
      const conflictsResponse = await axios.post(`${baseURL}/dietary-conflicts`, {
        restrictions: ['Keto', 'Vegan'],
        allergies: ['Nuts', 'Dairy'],
        dislikedIngredients: ['Onions']
      }, { headers });
      
      console.log('✅ Dietary conflicts endpoint successful');
      console.log('Conflicts detected:', conflictsResponse.data.conflicts.hasConflicts);
      if (conflictsResponse.data.conflicts.conflicts.length > 0) {
        console.log('Conflicts found:', conflictsResponse.data.conflicts.conflicts.length);
      }
    } catch (error) {
      console.error('❌ Dietary conflicts endpoint failed:', error.response?.data || error.message);
    }

    // Test 3: Goal-based suggestions
    console.log('\n3. Testing POST /goal-suggestions...');
    try {
      const goalSuggestionsResponse = await axios.post(`${baseURL}/goal-suggestions`, {
        goal: 'Lose Weight'
      }, { headers });
      
      console.log('✅ Goal suggestions endpoint successful');
      console.log('Recommended restrictions:', goalSuggestionsResponse.data.suggestions.recommendedRestrictions);
    } catch (error) {
      console.error('❌ Goal suggestions endpoint failed:', error.response?.data || error.message);
    }

    // Test 4: Health condition suggestions
    console.log('\n4. Testing POST /goal-suggestions with health condition...');
    try {
      const healthSuggestionsResponse = await axios.post(`${baseURL}/goal-suggestions`, {
        goal: 'Manage a Health Condition',
        healthCondition: 'Type 2 Diabetes'
      }, { headers });
      
      console.log('✅ Health condition suggestions endpoint successful');
      console.log('Recommended restrictions:', healthSuggestionsResponse.data.suggestions.recommendedRestrictions);
    } catch (error) {
      console.error('❌ Health condition suggestions endpoint failed:', error.response?.data || error.message);
    }

    // Test 5: Meal recommendations
    console.log('\n5. Testing POST /meal-recommendations...');
    try {
      const mealRecommendationsResponse = await axios.post(`${baseURL}/meal-recommendations`, {
        goalType: 'Lose Weight',
        limit: 5
      }, { headers });
      
      console.log('✅ Meal recommendations endpoint successful');
      console.log(`Found ${mealRecommendationsResponse.data.recommendations.length} meal recommendations`);
      if (mealRecommendationsResponse.data.recommendations.length > 0) {
        console.log('Sample recommendation:', mealRecommendationsResponse.data.recommendations[0].name);
      }
    } catch (error) {
      console.error('❌ Meal recommendations endpoint failed:', error.response?.data || error.message);
    }

    // Test 6: Chat
    console.log('\n6. Testing POST /chat...');
    try {
      const chatResponse = await axios.post(`${baseURL}/chat`, {
        message: 'What are some healthy Filipino breakfast options?',
        includeProfile: true,
        includeMeals: true
      }, { headers });
      
      console.log('✅ Chat endpoint successful');
      console.log('Chat response length:', chatResponse.data.response.length);
    } catch (error) {
      console.error('❌ Chat endpoint failed:', error.response?.data || error.message);
    }

    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    console.log('\n🎉 AI API endpoints testing completed!');

  } catch (error) {
    console.error('❌ Test setup failed:', error);
  }
}

testAIEndpoints();
