const mongoose = require('mongoose');
const User = require('../models/User');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster';

async function cleanupBadFavorites() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');

    // Find all users with favorite meal plans
    const users = await User.find({
      favoriteMealPlans: { $exists: true, $ne: [] }
    });

    console.log(`Found ${users.length} users with favorite meal plans`);

    let totalCleaned = 0;

    for (const user of users) {
      console.log(`\nChecking user: ${user.email}`);

      const originalCount = user.favoriteMealPlans.length;
      console.log(`Original favorite meal plans count: ${originalCount}`);

      // Check each favorite meal plan and remove ones with broken references
      const validPlans = [];

      for (const plan of user.favoriteMealPlans) {
        console.log(`\nChecking plan: ${plan.name} (ID: ${plan.plan})`);

        // Check if the referenced meal plan exists
        const MealPlan = require('../models/MealPlan');
        const referencedPlan = await MealPlan.findById(plan.plan);

        if (!referencedPlan) {
          console.log(`  ❌ Referenced meal plan ${plan.plan} does not exist - REMOVING`);
          totalCleaned++;
        } else {
          console.log(`  ✅ Referenced meal plan exists - KEEPING`);
          validPlans.push(plan);
        }
      }

      if (validPlans.length !== originalCount) {
        console.log(`\n  Updating user ${user.email}:`);
        console.log(`  - Original count: ${originalCount}`);
        console.log(`  - Valid count: ${validPlans.length}`);
        console.log(`  - Removed: ${originalCount - validPlans.length}`);

        user.favoriteMealPlans = validPlans;
        await user.save();
      } else {
        console.log(`  - No cleanup needed for this user`);
      }
    }

    console.log(`\n✅ Cleanup completed!`);
    console.log(`Total invalid favorite meal plans removed: ${totalCleaned}`);

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the cleanup
cleanupBadFavorites();
