const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const Meal = require('../models/Meal');
require('dotenv').config();

async function verifyImageSetup() {
  try {
    console.log('🔍 Verifying image setup...\n');
    
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    
    // Check meals in database
    const totalMeals = await Meal.countDocuments();
    console.log(`📊 Total meals in database: ${totalMeals}`);
    
    // Check image paths
    const mealsWithCorrectPaths = await Meal.countDocuments({ 
      image: { $regex: '^/imagesfood/' } 
    });
    console.log(`📊 Meals with correct image paths: ${mealsWithCorrectPaths}/${totalMeals}`);
    
    // Get sample meals
    const sampleMeals = await Meal.find().limit(5).select('name image');
    console.log('\n📋 Sample meals with image paths:');
    sampleMeals.forEach(meal => {
      console.log(`- ${meal.name}: ${meal.image}`);
    });
    
    // Check if image files exist
    const imageDir = path.join(__dirname, '../../public/imagesfood');
    console.log(`\n📁 Checking image directory: ${imageDir}`);
    
    if (!fs.existsSync(imageDir)) {
      console.error('❌ Image directory does not exist!');
      return;
    }
    
    const imageFiles = fs.readdirSync(imageDir).filter(file => 
      file.toLowerCase().endsWith('.jpg') || file.toLowerCase().endsWith('.png')
    );
    console.log(`📊 Total image files available: ${imageFiles.length}`);
    
    // Check if meal images exist
    let foundImages = 0;
    let missingImages = 0;
    
    console.log('\n🔍 Checking if meal images exist:');
    for (const meal of sampleMeals) {
      if (meal.image && meal.image.startsWith('/imagesfood/')) {
        const filename = meal.image.replace('/imagesfood/', '');
        const imagePath = path.join(imageDir, filename);
        
        if (fs.existsSync(imagePath)) {
          console.log(`✅ ${meal.name}: Image found (${filename})`);
          foundImages++;
        } else {
          console.log(`❌ ${meal.name}: Image missing (${filename})`);
          missingImages++;
        }
      }
    }
    
    console.log(`\n📊 Image verification: ${foundImages} found, ${missingImages} missing`);
    
    // Test image accessibility
    console.log('\n🌐 Testing image accessibility:');
    const testImageUrl = 'http://192.168.1.100:5000/imagesfood/adobo_manok.jpg';
    console.log(`Test URL: ${testImageUrl}`);
    console.log('You can test this URL in your browser or mobile app');
    
    // Show mobile app configuration
    console.log('\n📱 Mobile app configuration:');
    console.log('BASE_URL: http://192.168.1.100:5000/api');
    console.log('IMAGE_BASE_URL: http://192.168.1.100:5000');
    console.log('Image URL format: {IMAGE_BASE_URL}{meal.image}');
    console.log('Example: http://192.168.1.100:5000/imagesfood/adobo_manok.jpg');
    
    await mongoose.disconnect();
    console.log('\n✅ Verification complete!');
    
    if (mealsWithCorrectPaths === totalMeals && foundImages > 0) {
      console.log('\n🎉 SUCCESS: All images are properly configured!');
      console.log('📝 Next steps:');
      console.log('1. Make sure your backend server is running');
      console.log('2. Test the mobile app - images should now load correctly');
      console.log('3. If images still don\'t show, check your network connection');
    } else {
      console.log('\n⚠️  Some issues found. Please check the output above.');
    }
    
  } catch (error) {
    console.error('❌ Error during verification:', error);
  }
}

verifyImageSetup();
