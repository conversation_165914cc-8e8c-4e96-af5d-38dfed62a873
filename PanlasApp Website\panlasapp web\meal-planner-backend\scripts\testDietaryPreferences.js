const mongoose = require('mongoose');
const User = require('../models/User');
const Meal = require('../models/Meal');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Test dietary preferences functionality
const testDietaryPreferences = async () => {
  try {
    console.log('\n🧪 Testing Dietary Preferences System...\n');

    // Find a test user or create one
    let testUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!testUser) {
      console.log('📝 Creating test user...');
      testUser = new User({
        username: 'testuser',
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashedpassword123',
        dietaryPreferences: {
          restrictions: ['Vegetarian', 'Gluten-Free'],
          allergies: ['Nuts', 'Dairy'],
          dislikedIngredients: ['onions', 'mushrooms'],
          calorieTarget: 2000,
          mealFrequency: 3
        }
      });
      await testUser.save();
      console.log('✅ Test user created');
    } else {
      console.log('👤 Using existing test user');
      // Update preferences for testing
      testUser.dietaryPreferences = {
        restrictions: ['Vegetarian', 'Gluten-Free'],
        allergies: ['Nuts', 'Dairy'],
        dislikedIngredients: ['onions', 'mushrooms'],
        calorieTarget: 2000,
        mealFrequency: 3
      };
      await testUser.save();
      console.log('✅ Test user preferences updated');
    }

    console.log('📊 User preferences:', testUser.dietaryPreferences);

    // Test meal filtering based on preferences
    console.log('\n🔍 Testing meal filtering...');

    // Count total meals
    const totalMeals = await Meal.countDocuments();
    console.log(`📈 Total meals in database: ${totalMeals}`);

    // Count vegetarian meals
    const vegetarianMeals = await Meal.find({ 'dietType.isVegetarian': true });
    console.log(`🌱 Vegetarian meals: ${vegetarianMeals.length}`);

    // Count gluten-free meals
    const glutenFreeMeals = await Meal.find({ 'dietType.isGlutenFree': true });
    console.log(`🌾 Gluten-free meals: ${glutenFreeMeals.length}`);

    // Count meals that meet both restrictions
    const bothRestrictions = await Meal.find({
      'dietType.isVegetarian': true,
      'dietType.isGlutenFree': true
    });
    console.log(`✅ Meals meeting both restrictions: ${bothRestrictions.length}`);

    // Test calorie filtering
    const targetCaloriesPerMeal = testUser.dietaryPreferences.calorieTarget / testUser.dietaryPreferences.mealFrequency;
    const tolerance = targetCaloriesPerMeal * 0.3;
    
    console.log(`🎯 Target calories per meal: ${targetCaloriesPerMeal.toFixed(0)}`);
    console.log(`📏 Tolerance range: ${(targetCaloriesPerMeal - tolerance).toFixed(0)} - ${(targetCaloriesPerMeal + tolerance).toFixed(0)} calories`);

    const calorieFilteredMeals = await Meal.find({
      calories: {
        $gte: targetCaloriesPerMeal - tolerance,
        $lte: targetCaloriesPerMeal + tolerance
      }
    });
    console.log(`⚡ Meals within calorie range: ${calorieFilteredMeals.length}`);

    // Test complete filtering (restrictions + calories)
    const completeFilteredMeals = await Meal.find({
      'dietType.isVegetarian': true,
      'dietType.isGlutenFree': true,
      calories: {
        $gte: targetCaloriesPerMeal - tolerance,
        $lte: targetCaloriesPerMeal + tolerance
      }
    });
    console.log(`🎯 Meals meeting all criteria: ${completeFilteredMeals.length}`);

    // Show some example meals
    if (completeFilteredMeals.length > 0) {
      console.log('\n📋 Example meals meeting all criteria:');
      completeFilteredMeals.slice(0, 5).forEach((meal, index) => {
        console.log(`  ${index + 1}. ${meal.name} (${meal.calories || 0} cal)`);
        console.log(`     Vegetarian: ${meal.dietType?.isVegetarian || false}`);
        console.log(`     Gluten-Free: ${meal.dietType?.isGlutenFree || false}`);
      });
    }

    // Test allergy filtering
    console.log('\n🚫 Testing allergy filtering...');
    const mealsWithNuts = await Meal.find({
      ingredients: { $regex: 'nuts', $options: 'i' }
    });
    console.log(`🥜 Meals containing nuts: ${mealsWithNuts.length}`);

    const mealsWithDairy = await Meal.find({
      ingredients: { $regex: 'milk|cheese|butter|cream', $options: 'i' }
    });
    console.log(`🥛 Meals containing dairy: ${mealsWithDairy.length}`);

    // Test safe meals (no allergens)
    const safeMeals = await Meal.find({
      'dietType.isVegetarian': true,
      'dietType.isGlutenFree': true,
      ingredients: { 
        $not: { $regex: 'nuts|milk|cheese|butter|cream', $options: 'i' } 
      }
    });
    console.log(`✅ Safe meals (no allergens): ${safeMeals.length}`);

    // Test recommendation query simulation
    console.log('\n🎯 Testing recommendation query...');
    const recommendationQuery = {
      'dietType.isVegetarian': true,
      'dietType.isGlutenFree': true,
      ingredients: { 
        $not: { $regex: 'nuts|milk|cheese|butter|cream|onions|mushrooms', $options: 'i' } 
      },
      calories: {
        $gte: targetCaloriesPerMeal - tolerance,
        $lte: targetCaloriesPerMeal + tolerance
      }
    };

    console.log('🔍 Recommendation query:', JSON.stringify(recommendationQuery, null, 2));

    const recommendedMeals = await Meal.find(recommendationQuery)
      .limit(10)
      .sort({ rating: -1 });

    console.log(`🌟 Recommended meals found: ${recommendedMeals.length}`);

    if (recommendedMeals.length > 0) {
      console.log('\n🏆 Top recommended meals:');
      recommendedMeals.forEach((meal, index) => {
        console.log(`  ${index + 1}. ${meal.name}`);
        console.log(`     Calories: ${meal.calories || 0}`);
        console.log(`     Rating: ${meal.rating || 'N/A'}`);
        console.log(`     Vegetarian: ${meal.dietType?.isVegetarian || false}`);
        console.log(`     Gluten-Free: ${meal.dietType?.isGlutenFree || false}`);
        console.log('');
      });
    }

    // Test dietary statistics
    console.log('\n📊 Dietary Statistics Summary:');
    const stats = {
      total: totalMeals,
      vegetarian: vegetarianMeals.length,
      vegan: await Meal.countDocuments({ 'dietType.isVegan': true }),
      glutenFree: glutenFreeMeals.length,
      dairyFree: await Meal.countDocuments({ 'dietType.isDairyFree': true }),
      nutFree: await Meal.countDocuments({ 'dietType.isNutFree': true }),
      lowCarb: await Meal.countDocuments({ 'dietType.isLowCarb': true }),
      keto: await Meal.countDocuments({ 'dietType.isKeto': true }),
      pescatarian: await Meal.countDocuments({ 'dietType.isPescatarian': true }),
      halal: await Meal.countDocuments({ 'dietType.isHalal': true })
    };

    Object.entries(stats).forEach(([key, value]) => {
      const percentage = ((value / totalMeals) * 100).toFixed(1);
      console.log(`  ${key}: ${value} (${percentage}%)`);
    });

    console.log('\n✅ Dietary preferences testing completed successfully!');

  } catch (error) {
    console.error('❌ Error testing dietary preferences:', error);
  }
};

// Main execution function
const main = async () => {
  await connectDB();
  await testDietaryPreferences();
  
  console.log('\n🏁 Test completed!');
  process.exit(0);
};

// Run the test
main().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});
