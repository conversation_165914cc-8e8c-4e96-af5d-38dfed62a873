:root {
  --footer-bg: #222;
  --footer-accent: #20c5af;
  --footer-text: #fff;
  --footer-link: #ddd;
  --footer-link-hover: #20c5af;
  --footer-border: rgba(255, 255, 255, 0.1);
  --footer-social-bg: rgba(255, 255, 255, 0.08);
  --footer-social-hover: #20c5af;
}

.site-footer {
  background-color: var(--footer-bg);
  color: var(--footer-text);
  padding: 32px 0 10px;
  font-family: inherit;
  font-size: 0.97rem;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 18px;
  gap: 18px;
}

.footer-section {
  flex: 1;
  min-width: 180px;
  margin-bottom: 18px;
  padding-right: 10px;
}

.footer-section h3 {
  font-size: 1.05rem;
  margin-bottom: 10px;
  color: var(--footer-accent);
  position: relative;
  padding-bottom: 6px;
  letter-spacing: 0.3px;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 32px;
  height: 2px;
  background-color: var(--footer-accent);
  border-radius: 1px;
}

.about p {
  line-height: 1.5;
  margin-bottom: 10px;
  color: var(--footer-link);
}

.social-links {
  display: flex;
  gap: 10px;
}

.social-links a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: var(--footer-social-bg);
  border-radius: 50%;
  color: var(--footer-text);
  text-align: center;
  font-size: 1rem;
  transition: background 0.3s, color 0.3s, transform 0.2s;
  outline: none;
  border: none;
}

.social-links a:hover,
.social-links a:focus {
  background-color: var(--footer-social-hover);
  color: var(--footer-bg);
  transform: translateY(-2px) scale(1.04);
  box-shadow: 0 1px 4px rgba(32,197,175,0.10);
}

.links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.links ul li {
  margin-bottom: 7px;
}

.links ul li a {
  color: var(--footer-link);
  text-decoration: none;
  transition: color 0.3s, padding-left 0.3s;
  padding-left: 0;
  outline: none;
  font-size: 0.97em;
}

.links ul li a:hover,
.links ul li a:focus {
  color: var(--footer-link-hover);
  padding-left: 3px;
  text-decoration: underline;
}

.contact p {
  margin-bottom: 7px;
  display: flex;
  align-items: center;
  color: var(--footer-link);
  font-size: 0.97em;
}

.contact p i,
.contact p svg {
  margin-right: 7px;
  color: var(--footer-accent);
  font-size: 1em;
}

.footer-bottom {
  border-top: 1px solid var(--footer-border);
  padding-top: 10px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  font-size: 0.92rem;
  color: var(--footer-link);
  gap: 10px;
}

.footer-bottom p {
  margin-bottom: 5px;
}

.footer-bottom-links {
  display: flex;
  gap: 12px;
  align-items: center;
}

.footer-bottom-links a {
  color: var(--footer-link);
  text-decoration: none;
  transition: color 0.3s;
  outline: none;
  font-size: 0.97em;
}

.footer-bottom-links a:hover,
.footer-bottom-links a:focus {
  color: var(--footer-link-hover);
  text-decoration: underline;
}

@media (max-width: 900px) {
  .footer-content {
    flex-direction: column;
    gap: 0;
  }
  .footer-section {
    margin-bottom: 18px;
    padding-right: 0;
  }
}

@media (max-width: 600px) {
  .site-footer {
    padding: 18px 0 6px;
    font-size: 0.95rem;
  }
  .footer-content {
    margin-bottom: 10px;
  }
  .footer-section {
    min-width: 0;
    margin-bottom: 12px;
  }
  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 5px;
  }
  .footer-bottom-links {
    justify-content: center;
    margin-top: 6px;
    gap: 8px;
  }
}
