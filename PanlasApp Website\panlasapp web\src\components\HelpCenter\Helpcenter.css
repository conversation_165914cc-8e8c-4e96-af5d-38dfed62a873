/* Help Center Styles */
.help-center-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e0e0e0;
}

.help-center-header h1 {
  margin: 0;
  color: #333;
  font-size: 2.5rem;
  font-weight: 700;
}

.feedback-button {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.feedback-button:hover {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.feedback-success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border-left: 4px solid #4CAF50;
  font-weight: 500;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.help-content {
  max-width: 800px;
}

.help-section {
  margin-bottom: 3rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.help-section h2 {
  color: #4CAF50;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.help-section h3 {
  color: #333;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #4CAF50;
  padding-bottom: 0.5rem;
}

.help-section p {
  color: #555;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.help-section ul {
  color: #555;
  line-height: 1.6;
  padding-left: 1.5rem;
}

.help-section li {
  margin-bottom: 0.5rem;
}

.help-item {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f9f9f9;
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-item h4 {
  color: #333;
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.help-item p {
  margin-bottom: 0;
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .help-center-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .help-center-header h1 {
    font-size: 2rem;
  }
  
  .feedback-button {
    align-self: stretch;
    justify-content: center;
  }
  
  .help-section {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .help-section h2 {
    font-size: 1.5rem;
  }
  
  .help-section h3 {
    font-size: 1.3rem;
  }
  
  .help-item {
    padding: 1rem;
  }
  
  .help-item h4 {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .help-center-header h1 {
    font-size: 1.8rem;
  }
  
  .feedback-button {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
  
  .help-section {
    padding: 1rem;
    border-radius: 8px;
  }
  
  .help-section h2 {
    font-size: 1.4rem;
  }
  
  .help-section h3 {
    font-size: 1.2rem;
  }
  
  .help-item {
    padding: 0.75rem;
    margin-bottom: 1.5rem;
  }
  
  .help-item h4 {
    font-size: 1rem;
  }
}
