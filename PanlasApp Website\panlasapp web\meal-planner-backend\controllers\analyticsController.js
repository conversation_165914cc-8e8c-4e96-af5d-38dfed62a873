const Analytics = require('../models/Analytics');
const User = require('../models/User');
const mongoose = require('mongoose');

// Get comprehensive dashboard analytics
exports.getDashboardAnalytics = async (req, res) => {
  try {
    const { timeRange = '7d', userType = 'all' } = req.query;

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();

    switch (timeRange) {
      case '1d':
        startDate.setDate(endDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }

    // Get user statistics directly from User collection (not from Analytics events)
    const User = require('../models/User');

    // Build user query criteria - only verified users
    let userQuery = { isEmailVerified: true };

    if (userType === 'admin') {
      userQuery.isAdmin = true;
    } else if (userType === 'user') {
      userQuery.isAdmin = false;
    }

    // Get user counts directly from User collection
    const totalUsers = await User.countDocuments(userQuery);
    const activeUsers = await User.countDocuments({ ...userQuery, isActive: true });
    const adminUsers = await User.countDocuments({ ...userQuery, isAdmin: true });
    const regularUsers = await User.countDocuments({ ...userQuery, isAdmin: false });

    const userStats = [{
      _id: null,
      totalUsers,
      activeUsers,
      adminUsers,
      regularUsers
    }];

    // Build match criteria for Analytics events
    const matchCriteria = {
      timestamp: { $gte: startDate, $lte: endDate },
      'userContext.isVerified': true // Only verified users
    };

    if (userType === 'admin') {
      matchCriteria['userContext.isAdmin'] = true;
    } else if (userType === 'user') {
      matchCriteria['userContext.isAdmin'] = false;
    }

    // Get event statistics
    const eventStats = await Analytics.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: '$event',
          count: { $sum: 1 },
          uniqueUsers: { $addToSet: '$user' }
        }
      },
      {
        $project: {
          event: '$_id',
          count: 1,
          uniqueUsers: { $size: '$uniqueUsers' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get platform statistics
    const platformStats = await Analytics.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: '$deviceInfo.platform',
          count: { $sum: 1 },
          uniqueUsers: { $addToSet: '$user' }
        }
      },
      {
        $project: {
          platform: '$_id',
          count: 1,
          uniqueUsers: { $size: '$uniqueUsers' }
        }
      }
    ]);

    // Get daily activity trends
    const dailyTrends = await Analytics.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
            userType: '$userContext.userType'
          },
          events: { $sum: 1 },
          uniqueUsers: { $addToSet: '$user' }
        }
      },
      {
        $group: {
          _id: '$_id.date',
          admin: {
            $sum: { $cond: [{ $eq: ['$_id.userType', 'admin'] }, '$events', 0] }
          },
          user: {
            $sum: { $cond: [{ $eq: ['$_id.userType', 'user'] }, '$events', 0] }
          },
          totalEvents: { $sum: '$events' },
          uniqueUsers: { $addToSet: '$uniqueUsers' }
        }
      },
      {
        $project: {
          date: '$_id',
          admin: 1,
          user: 1,
          totalEvents: 1,
          uniqueUsers: { $size: { $reduce: { input: '$uniqueUsers', initialValue: [], in: { $setUnion: ['$$value', '$$this'] } } } }
        }
      },
      { $sort: { date: 1 } }
    ]);

    // Get login/logout statistics
    const sessionStats = await Analytics.aggregate([
      {
        $match: {
          ...matchCriteria,
          event: { $in: ['login', 'logout'] }
        }
      },
      {
        $group: {
          _id: {
            event: '$event',
            userType: '$userContext.userType'
          },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.event',
          admin: {
            $sum: { $cond: [{ $eq: ['$_id.userType', 'admin'] }, '$count', 0] }
          },
          user: {
            $sum: { $cond: [{ $eq: ['$_id.userType', 'user'] }, '$count', 0] }
          },
          total: { $sum: '$count' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        timeRange,
        userType,
        dateRange: { startDate, endDate },
        userStats: userStats[0] || {
          totalUsers: 0,
          activeUsers: 0,
          adminUsers: 0,
          regularUsers: 0
        },
        eventStats,
        platformStats,
        dailyTrends,
        sessionStats
      }
    });

  } catch (error) {
    console.error('Dashboard analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching dashboard analytics',
      error: error.message
    });
  }
};

// Get user activity analytics
exports.getUserActivityAnalytics = async (req, res) => {
  try {
    const { timeRange = '30d', status = 'all' } = req.query;
    
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(timeRange.replace('d', '')));

    const matchCriteria = {
      timestamp: { $gte: startDate, $lte: endDate },
      'userContext.isVerified': true
    };

    if (status === 'active') {
      matchCriteria['userContext.isActive'] = true;
    } else if (status === 'inactive') {
      matchCriteria['userContext.isActive'] = false;
    }

    // Get user activity breakdown
    const userActivity = await Analytics.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: {
            user: '$user',
            userType: '$userContext.userType',
            isActive: '$userContext.isActive'
          },
          totalEvents: { $sum: 1 },
          lastActivity: { $max: '$timestamp' },
          events: { $push: '$event' }
        }
      },
      {
        $group: {
          _id: {
            userType: '$_id.userType',
            isActive: '$_id.isActive'
          },
          userCount: { $sum: 1 },
          avgEvents: { $avg: '$totalEvents' },
          totalEvents: { $sum: '$totalEvents' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        timeRange,
        status,
        userActivity
      }
    });

  } catch (error) {
    console.error('User activity analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching user activity analytics',
      error: error.message
    });
  }
};

// Get platform and device analytics
exports.getPlatformAnalytics = async (req, res) => {
  try {
    const { timeRange = '30d' } = req.query;
    
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(timeRange.replace('d', '')));

    const matchCriteria = {
      timestamp: { $gte: startDate, $lte: endDate },
      'userContext.isVerified': true
    };

    // Platform usage by user type
    const platformUsage = await Analytics.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: {
            platform: '$deviceInfo.platform',
            userType: '$userContext.userType'
          },
          count: { $sum: 1 },
          uniqueUsers: { $addToSet: '$user' }
        }
      },
      {
        $group: {
          _id: '$_id.platform',
          admin: {
            $sum: { $cond: [{ $eq: ['$_id.userType', 'admin'] }, '$count', 0] }
          },
          user: {
            $sum: { $cond: [{ $eq: ['$_id.userType', 'user'] }, '$count', 0] }
          },
          totalEvents: { $sum: '$count' },
          uniqueUsers: { $addToSet: '$uniqueUsers' }
        }
      },
      {
        $project: {
          platform: '$_id',
          admin: 1,
          user: 1,
          totalEvents: 1,
          uniqueUsers: { $size: { $reduce: { input: '$uniqueUsers', initialValue: [], in: { $setUnion: ['$$value', '$$this'] } } } }
        }
      }
    ]);

    // Browser usage
    const browserUsage = await Analytics.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: '$deviceInfo.browser',
          count: { $sum: 1 },
          uniqueUsers: { $addToSet: '$user' }
        }
      },
      {
        $project: {
          browser: '$_id',
          count: 1,
          uniqueUsers: { $size: '$uniqueUsers' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      success: true,
      data: {
        timeRange,
        platformUsage,
        browserUsage
      }
    });

  } catch (error) {
    console.error('Platform analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching platform analytics',
      error: error.message
    });
  }
};

// Track custom event
exports.trackEvent = async (req, res) => {
  try {
    const { event, eventData = {}, deviceInfo = {} } = req.body;
    
    if (!event) {
      return res.status(400).json({
        success: false,
        message: 'Event name is required'
      });
    }

    // Get user context
    const user = await User.findById(req.user.id);
    if (!user || !user.isEmailVerified) {
      return res.status(400).json({
        success: false,
        message: 'User not found or not verified'
      });
    }

    const analyticsData = {
      user: req.user.id,
      sessionId: req.headers['x-session-id'] || require('uuid').v4(),
      event,
      eventData,
      deviceInfo: {
        ...deviceInfo,
        userAgent: req.headers['user-agent']
      },
      location: {
        ipAddress: req.ip || req.connection.remoteAddress
      },
      userContext: {
        isAdmin: user.isAdmin || false,
        isActive: user.isActive || false,
        isVerified: user.isEmailVerified || false,
        userType: user.isAdmin ? 'admin' : 'user'
      }
    };

    await Analytics.create(analyticsData);

    res.json({
      success: true,
      message: 'Event tracked successfully'
    });

  } catch (error) {
    console.error('Track event error:', error);
    res.status(500).json({
      success: false,
      message: 'Error tracking event',
      error: error.message
    });
  }
};
