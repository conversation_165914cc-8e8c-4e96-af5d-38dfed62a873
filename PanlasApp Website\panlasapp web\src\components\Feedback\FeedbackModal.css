/* Feedback Modal Styles */
.feedback-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.feedback-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feedback-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border-radius: 12px 12px 0 0;
}

.feedback-modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.feedback-modal-close {
  background: none;
  border: none;
  font-size: 2rem;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.feedback-modal-close:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.2);
}

.feedback-modal-close:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.feedback-form {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  font-family: inherit;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #f44336;
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.char-count {
  font-size: 0.8rem;
  color: #666;
  text-align: right;
  margin-top: 0.25rem;
}

.rating-container select {
  font-size: 0.95rem;
}

.error-message {
  color: #f44336;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: block;
}

.submit-error {
  background-color: #ffebee;
  padding: 0.75rem;
  border-radius: 6px;
  border-left: 4px solid #f44336;
  margin-bottom: 1rem;
}

.feedback-modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e0e0e0;
}

.btn-cancel,
.btn-submit {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
  border: 2px solid #e0e0e0;
}

.btn-cancel:hover:not(:disabled) {
  background-color: #e0e0e0;
  color: #333;
}

.btn-submit {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.btn-submit:hover:not(:disabled) {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  transform: translateY(-1px);
}

.btn-cancel:disabled,
.btn-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .feedback-modal {
    width: 95%;
    margin: 1rem;
  }
  
  .feedback-modal-header {
    padding: 1rem 1.5rem;
  }
  
  .feedback-modal-header h2 {
    font-size: 1.3rem;
  }
  
  .feedback-form {
    padding: 1.5rem;
  }
  
  .feedback-modal-actions {
    flex-direction: column-reverse;
  }
  
  .btn-cancel,
  .btn-submit {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .feedback-modal {
    width: 100%;
    height: 100%;
    border-radius: 0;
    max-height: 100vh;
  }
  
  .feedback-modal-header {
    border-radius: 0;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 0.6rem;
  }
}
