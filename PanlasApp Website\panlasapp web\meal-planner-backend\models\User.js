const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true
  },
  firstName: {
    type: String,
    trim: true
  },
  lastName: {
    type: String,
    trim: true
  },
  dateOfBirth: {
    type: Date
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other', 'prefer not to say']
  },
  barangay: {
    type: String,
    trim: true
  },
  lastLogin: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  // Admin field
  isAdmin: {
    type: Boolean,
    default: false
  },
  // Account status fields
  isActive: {
    type: Boolean,
    default: true
  },
  disabledAt: {
    type: Date
  },
  disabledBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  // Email verification fields
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: {
    type: String
  },
  emailVerificationExpires: {
    type: Date
  },
  // OTP verification fields
  emailOTP: {
    type: String
  },
  emailOTPExpires: {
    type: Date
  },
  // Password reset fields
  passwordResetToken: {
    type: String
  },
  passwordResetExpires: {
    type: Date
  },
  // New fields for dietary preferences
  dietaryPreferences: {
    restrictions: [String], // Vegetarian, Vegan, Gluten-Free, etc.
    allergies: [String], // List of allergies
    dislikedIngredients: [String], // List of disliked ingredients
    calorieTarget: Number, // Daily calorie target
    macroTargets: {
      protein: Number, // in grams
      carbs: Number, // in grams
      fat: Number // in grams
    },
    mealFrequency: {
      type: Number,
      default: 3 // Number of meals per day
    }
  },
  // Favorite meals
  favoriteMeals: [{
    name: String,
    mealType: [String],
    category: String,
    dietaryTags: [String],
    rating:[String],
    calories: Number,
    protein: Number,
    carbs: Number,
    fat: Number,
    image: String,
    description: String,
    ingredients: [String],
    instructions: [String]
  }],

  recentlyViewedMeals: [{
  name: String,
  mealType: [String],
  category: String,
  dietaryTags: [String],
  rating: [String],
  calories: Number,
  protein: Number,
  carbs: Number,
  fat: Number,
  image: String,
  description: String,
  ingredients: [String],
  instructions: [String],

}],

  // Recently added to meal plans
  recentlyAddedToMealPlans: [{
    name: String,
    mealType: [String],
    category: String,
    dietaryTags: [String],
    rating: [String],
    calories: Number,
    protein: Number,
    carbs: Number,
    fat: Number,
    image: String,
    description: String,
    ingredients: [String],
    instructions: [String],
    addedAt: {
      type: Date,
      default: Date.now
    },
    addedToDate: String, // The date it was added to in meal plan
    addedToMealType: String // breakfast, lunch, dinner, snack
  }],

familyMembers: [{
  name: String,
  dateOfBirth: {
    type: Date
  },
  dietaryPreferences: {
    restrictions: [String],
    allergies: [String],
    dislikedIngredients: [String],
    calorieTarget: Number,
    macroTargets: {
      protein: Number,
      carbs: Number,
      fat: Number
    },
    mealFrequency: Number
  }
}],

  // Favorite meal plans (renamed from savedMealPlans)
  favoriteMealPlans: [{
    name: String,
    plan: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'MealPlan'
    },
    date: String,
    totalCalories: Number,
    totalMeals: Number,
    mealTypes: [String], // breakfast, lunch, dinner, snack
    createdAt: {
      type: Date,
      default: Date.now
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }]
});

// Pre-save hook to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model('User', userSchema);

module.exports = User;
