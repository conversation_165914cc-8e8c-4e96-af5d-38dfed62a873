.admin-dashboard {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.admin-dashboard h1 {
  margin-bottom: 2rem;
  color: #333;
}

/* Add these styles to match your app's theme */
.admin-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #ddd;
  overflow-x: auto;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 1rem;
  font-weight: 500;
  color: #555;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.tab-button:hover {
  color: #333;
  background-color: #f5f5f5;
}

.tab-button.active {
  color: #4CAF50; /* Match your app's primary color */
  border-bottom-color: #4CAF50; /* Match your app's primary color */
  font-weight: bold;
}

.tab-content {
  min-height: 400px;
}

.dashboard-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-box {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.stat-box h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #4CAF50; /* Match your app's primary color */
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.admin-table th,
.admin-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.admin-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #333;
}

.admin-table tr:hover {
  background-color: #f9f9f9;
}

/* Action buttons for user management */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-buttons button {
  padding: 0.4rem 0.8rem;
  border: none;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.btn-make-admin {
  background-color: #4CAF50;
  color: white;
}

.btn-make-admin:hover {
  background-color: #45a049;
  transform: translateY(-1px);
}

.btn-remove-admin {
  background-color: #ff9800;
  color: white;
}

.btn-remove-admin:hover {
  background-color: #f57c00;
  transform: translateY(-1px);
}

.btn-disable {
  background-color: #f44336;
  color: white;
}

.btn-disable:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
}

.btn-enable {
  background-color: #4CAF50;
  color: white;
}

.btn-enable:hover {
  background-color: #45a049;
  transform: translateY(-1px);
}

/* Status badges */
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.active {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #4caf50;
}

.status-badge.disabled {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #f44336;
}

.status-badge.verified {
  background-color: #e3f2fd;
  color: #1565c0;
  border: 1px solid #2196f3;
}

.status-badge.unverified {
  background-color: #fff3e0;
  color: #ef6c00;
  border: 1px solid #ff9800;
}

/* Disabled user row styling */
.disabled-user {
  opacity: 0.6;
  background-color: #fafafa;
}

.disabled-user td {
  color: #666;
}

/* Disabled button styling */
.action-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.chart-section {
  margin-top: 2rem;
  height: 400px;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.error {
  text-align: center;
  padding: 2rem;
  color: #f44336;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }

  .admin-table {
    font-size: 0.9rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.5rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.3rem;
  }

  .action-buttons button {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }
}

/* User Statistics Grid */
.user-stats-grid {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.user-stats-grid .stat-item {
  transition: transform 0.2s ease;
}

.user-stats-grid .stat-item:hover {
  transform: translateY(-2px);
}

/* User Filters */
.user-filters {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.user-filters label {
  color: #495057;
  font-weight: 600;
}

.user-filters input,
.user-filters select {
  transition: all 0.2s ease;
  background-color: white;
}

.user-filters input:focus,
.user-filters select:focus {
  outline: none;
  border-color: #20C5AF;
  box-shadow: 0 0 0 2px rgba(32, 197, 175, 0.2);
}

.user-filters input:hover,
.user-filters select:hover {
  border-color: #20C5AF;
}

/* Filter Results Info */
.filter-results-info {
  color: #6c757d;
  font-style: italic;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

/* Enhanced Table Styling */
.admin-table {
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border-radius: 8px;
  overflow: hidden;
}

.admin-table thead th {
  background: linear-gradient(135deg, #20C5AF 0%, #17a2b8 100%);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.admin-table tbody tr {
  transition: all 0.2s ease;
}

.admin-table tbody tr:hover {
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* No Results Message */
.no-results {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  color: #6c757d;
  font-style: italic;
}

@media (max-width: 768px) {
  .user-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 10px;
    padding: 10px;
  }

  .user-filters {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 10px;
  }

  .user-filters input,
  .user-filters select {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .admin-tabs {
    flex-wrap: wrap;
  }

  .tab-button {
    flex: 1 0 auto;
    text-align: center;
    padding: 0.5rem 1rem;
  }

  .user-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .admin-table {
    font-size: 0.8rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.4rem;
  }
}
