# PanlasApp Frontend

A modern Filipino meal planning application built with React and Vite.

## Features

- **Meal Planning**: Plan your Filipino meals for the week with an intuitive calendar interface
- **Nutritional Tracking**: Track calories, protein, carbs, and fats for each meal
- **Recipe Management**: Browse and manage a comprehensive database of Filipino recipes
- **User Authentication**: Secure login and registration with JWT tokens
- **Responsive Design**: Fully responsive design that works on desktop, tablet, and mobile
- **Interactive Charts**: Visualize nutritional data with Chart.js
- **Meal Recommendations**: Get AI-powered meal suggestions based on preferences
- **Favorites System**: Save and organize your favorite meals
- **Admin Dashboard**: Administrative interface for content management

## Tech Stack

- **Frontend Framework**: React 19 with Vite for fast development and building
- **UI Library**: Material-UI (MUI) for consistent, modern design components
- **Charts**: Chart.js with react-chartjs-2 for data visualization
- **Routing**: React Router DOM for client-side navigation
- **HTTP Client**: Axios for API communication
- **Icons**: React Icons for comprehensive icon library
- **Date Handling**: date-fns for date manipulation and formatting
- **State Management**: React Context API for global state
- **Styling**: CSS modules and MUI theming

## Getting Started

### Prerequisites

- Node.js 18 or higher
- npm or yarn package manager
- Backend API running (see meal-planner-backend)

### Local Development

1. **Navigate to the frontend directory**:
   ```bash
   cd "PanlasApp Website/panlasapp web"
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   ```

4. **Update environment variables** in `.env.local`:
   ```
   VITE_API_URL=http://localhost:5000/api
   ```

5. **Start the development server**:
   ```bash
   npm run dev
   ```

The application will be available at `http://localhost:3000`

### Production Deployment (Vercel)

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Deploy to Vercel**:
   - Connect your GitHub repository to Vercel
   - Set root directory to: `PanlasApp Website/panlasapp web`
   - Configure environment variables in Vercel dashboard
   - Vercel will automatically deploy on every push to main branch

## Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build optimized production bundle
- `npm run preview` - Preview production build locally
- `npm run start` - Start production server (for deployment)
- `npm run lint` - Run ESLint for code quality
- `npm run lint:fix` - Fix ESLint issues automatically
- `npm run clean` - Clean build directory

## Environment Variables

See `.env.example` for all available environment variables:

### Required
- `VITE_API_URL` - Backend API URL (e.g., `https://your-backend.railway.app/api`)

### Optional
- `VITE_NODE_ENV` - Environment mode (development/production)
- `VITE_ANALYTICS_ENABLED` - Enable analytics tracking
- `VITE_DEBUG` - Enable debug mode

## Deployment

See `DEPLOYMENT.md` for detailed deployment instructions for both development and production environments.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
