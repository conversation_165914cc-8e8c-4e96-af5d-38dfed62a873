// middleware/auth.js
const jwt = require('jsonwebtoken');

// Required authentication middleware
const auth = (req, res, next) => {
  try {
    // Get token from header - support both formats
    let token = null;

    // Check for Authorization: Bearer <token>
    const authHeader = req.header('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.replace('Bearer ', '');
    }

    // Check for x-auth-token: <token> (legacy format)
    if (!token) {
      token = req.header('x-auth-token');
    }

    if (!token) {
      return res.status(401).json({ message: 'Authentication failed: No token provided' });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Add user from payload
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ message: 'Authentication failed' });
  }
};

// Optional authentication middleware - doesn't fail if no token
const optionalAuth = (req, res, next) => {
  try {
    // Get token from header - support both formats
    let token = null;

    // Check for Authorization: Bearer <token>
    const authHeader = req.header('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.replace('Bearer ', '');
    }

    // Check for x-auth-token: <token> (legacy format)
    if (!token) {
      token = req.header('x-auth-token');
    }

    if (token) {
      // Verify token if present
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      req.user = decoded;
    }
    // Continue regardless of whether token is present or valid
    next();
  } catch (error) {
    // Continue without user if token is invalid
    next();
  }
};

module.exports = auth;
module.exports.optionalAuth = optionalAuth;
