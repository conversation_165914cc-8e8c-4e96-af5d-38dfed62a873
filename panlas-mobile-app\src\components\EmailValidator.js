import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../styles/theme';

const EmailValidator = ({ email, showValidation = false }) => {
  const validateEmail = (email) => {
    if (!email) return { isValid: true, errors: [] };
    
    const errors = [];
    
    // Check for @ symbol
    if (!email.includes('@')) {
      errors.push('Email must contain @ symbol');
    }
    
    // Check for domain
    if (email.includes('@')) {
      const parts = email.split('@');
      if (parts.length !== 2) {
        errors.push('Invalid email format');
      } else {
        const [localPart, domain] = parts;
        
        // Check local part
        if (!localPart || localPart.length === 0) {
          errors.push('Email must have text before @');
        }
        
        // Check domain
        if (!domain || domain.length === 0) {
          errors.push('Email must have domain after @');
        } else if (!domain.includes('.')) {
          errors.push('Domain must contain a dot (.)');
        } else {
          const domainParts = domain.split('.');
          if (domainParts.some(part => part.length === 0)) {
            errors.push('Invalid domain format');
          }
        }
      }
    }
    
    // Check for spaces
    if (email.includes(' ')) {
      errors.push('Email cannot contain spaces');
    }
    
    // Check for consecutive dots
    if (email.includes('..')) {
      errors.push('Email cannot have consecutive dots');
    }
    
    // Check if starts or ends with dot
    if (email.startsWith('.') || email.endsWith('.')) {
      errors.push('Email cannot start or end with a dot');
    }
    
    // Basic regex check for overall format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (email && !emailRegex.test(email)) {
      if (errors.length === 0) {
        errors.push('Invalid email format');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const validation = validateEmail(email);
  
  if (!showValidation || !email) return null;

  return (
    <View style={styles.container}>
      {validation.isValid ? (
        <View style={styles.validContainer}>
          <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
          <Text style={styles.validText}>Valid email format</Text>
        </View>
      ) : (
        <View style={styles.errorContainer}>
          {validation.errors.map((error, index) => (
            <View key={index} style={styles.errorItem}>
              <Ionicons name="alert-circle" size={16} color="#FF5252" />
              <Text style={styles.errorText}>{error}</Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 4,
    marginBottom: 8,
  },
  validContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  validText: {
    fontSize: 12,
    color: '#4CAF50',
    marginLeft: 6,
    fontWeight: '500',
  },
  errorContainer: {
    marginTop: 4,
  },
  errorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  errorText: {
    fontSize: 12,
    color: '#FF5252',
    marginLeft: 6,
    flex: 1,
  },
});

export default EmailValidator;
