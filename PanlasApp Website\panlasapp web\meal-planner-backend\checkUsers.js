const mongoose = require('mongoose');
const User = require('./models/User');

async function checkUserVerificationStatus() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster');
    
    console.log('🔍 DATABASE USER VERIFICATION STATUS REPORT');
    console.log('='.repeat(60));
    
    // Get total users
    const totalUsers = await User.countDocuments();
    console.log(`📊 TOTAL USERS: ${totalUsers}`);
    
    // Get verified users
    const verifiedUsers = await User.countDocuments({ isEmailVerified: true });
    console.log(`✅ VERIFIED USERS: ${verifiedUsers}`);
    
    // Get unverified users
    const unverifiedUsers = await User.countDocuments({ isEmailVerified: false });
    console.log(`❌ UNVERIFIED USERS: ${unverifiedUsers}`);
    
    // Get users with null/undefined verification status
    const nullVerificationUsers = await User.countDocuments({ 
      $or: [
        { isEmailVerified: null },
        { isEmailVerified: { $exists: false } }
      ]
    });
    console.log(`❓ NULL/UNDEFINED VERIFICATION: ${nullVerificationUsers}`);
    
    console.log('\n📈 PERCENTAGE BREAKDOWN:');
    console.log(`✅ Verified: ${((verifiedUsers / totalUsers) * 100).toFixed(1)}%`);
    console.log(`❌ Unverified: ${((unverifiedUsers / totalUsers) * 100).toFixed(1)}%`);
    if (nullVerificationUsers > 0) {
      console.log(`❓ Null/Undefined: ${((nullVerificationUsers / totalUsers) * 100).toFixed(1)}%`);
    }
    
    // Check admin users verification status
    console.log('\n👑 ADMIN USERS VERIFICATION STATUS:');
    const adminUsers = await User.find({ isAdmin: true })
      .select('username email isEmailVerified isAdmin createdAt')
      .sort({ createdAt: -1 });
    
    adminUsers.forEach((user, index) => {
      const verificationStatus = user.isEmailVerified ? '✅ Verified' : '❌ Unverified';
      console.log(`${index + 1}. ${user.username} (${user.email}) - ${verificationStatus}`);
    });
    
    console.log('\n📋 SUMMARY FOR ADMIN DASHBOARD:');
    console.log('='.repeat(60));
    console.log(`Total Users in Database: ${totalUsers}`);
    console.log(`Users Counted in Admin Dashboard: ${verifiedUsers} (verified only)`);
    console.log(`Users Excluded from Dashboard: ${unverifiedUsers} (unverified)`);
    console.log(`Verification Rate: ${((verifiedUsers / totalUsers) * 100).toFixed(1)}%`);
    console.log('='.repeat(60));
    
    // Get some sample users for verification
    console.log('\n👥 SAMPLE VERIFIED USERS:');
    const sampleVerified = await User.find({ isEmailVerified: true })
      .select('username email isEmailVerified createdAt')
      .limit(3)
      .sort({ createdAt: -1 });
    
    sampleVerified.forEach((user, index) => {
      console.log(`${index + 1}. ${user.username} (${user.email}) - Created: ${user.createdAt.toLocaleDateString()}`);
    });
    
    console.log('\n👥 SAMPLE UNVERIFIED USERS:');
    const sampleUnverified = await User.find({ isEmailVerified: false })
      .select('username email isEmailVerified createdAt')
      .limit(3)
      .sort({ createdAt: -1 });
    
    sampleUnverified.forEach((user, index) => {
      console.log(`${index + 1}. ${user.username} (${user.email}) - Created: ${user.createdAt.toLocaleDateString()}`);
    });
    
    await mongoose.disconnect();
    console.log('\n✅ Database check completed successfully!');
    
  } catch (error) {
    console.error('❌ Error checking database:', error.message);
    process.exit(1);
  }
}

checkUserVerificationStatus();
