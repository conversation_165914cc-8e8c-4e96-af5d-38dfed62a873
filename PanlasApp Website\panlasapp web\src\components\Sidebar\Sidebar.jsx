/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import axios from "axios";
import apiService from "../../../meal-planner-backend/services/apiService";
import { useFavorites } from "../Favorites/FavoritesContext";
import { FaTimes, FaHeart, FaRegHeart, FaCalendarPlus } from "react-icons/fa";
import { useAdminView } from "../../context/AdminViewContext";

// Convert price range to peso signs
const getPesoSigns = (priceRange) => {
  switch (priceRange) {
    case "Low": return "₱";
    case "Mid": return "₱₱";
    case "High": return "₱₱₱";
    default: return "₱";
  }
};

const Sidebar = ({ isActive }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isAdmin, setIsAdmin] = useState(false);
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [recommendations, setRecommendations] = useState([]);
  const [recommendationsLoading, setRecommendationsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState(null);
  const [notification, setNotification] = useState({ show: false, message: "" });

  // Favorites context
  const { addFavorite, removeFavorite, isFavorite } = useFavorites();
  const { isViewingAsUser } = useAdminView();
  
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!apiService.isAuthenticated()) {
        navigate('/login');
        return;
      }
      
      try {
        setIsLoading(true);
        const userData = await apiService.getUserProfile();
        setUser(userData);
        setIsAdmin(userData.isAdmin || false);
      } catch (error) {
        console.error('Error fetching user profile:', error);
        if (error.message.includes('Authentication failed')) {
          apiService.logout();
          navigate('/login');
        }
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUserProfile();
  }, [navigate]);

  // Fetch meal recommendations - only when in user view
  useEffect(() => {
    const fetchRecommendations = async () => {
      // Only fetch recommendations if user is not admin or is viewing as user
      if (!apiService.isAuthenticated() || (isAdmin && !isViewingAsUser)) {
        setRecommendations([]);
        return;
      }

      try {
        setRecommendationsLoading(true);
        const token = localStorage.getItem('token');

        // Fetch Filipino dishes as recommendations
        const response = await axios.get('http://localhost:5000/api/meals/filipino', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'x-auth-token': token
          }
        });

        if (response.data && Array.isArray(response.data)) {
          // Shuffle and get random recommendations
          const shuffled = response.data.sort(() => 0.5 - Math.random());
          setRecommendations(shuffled.slice(0, 5)); // Limit to 5 recommendations
        }
      } catch (error) {
        console.error('Error fetching recommendations:', error);
        setRecommendations([]);
      } finally {
        setRecommendationsLoading(false);
      }
    };

    fetchRecommendations();
  }, [user, isAdmin, isViewingAsUser]);

  // Check if the current path matches the link
  const isActiveLink = (path) => {
    return location.pathname === path;
  };

  const handleLogout = () => {
    apiService.logout();
    navigate('/login');
  };

  const handleRecommendationClick = (meal) => {
    setSelectedMeal(meal);
    setShowModal(true);
  };

  const closeMealDetails = () => {
    setShowModal(false);
    setSelectedMeal(null);
  };

  const toggleFavorite = (e, dish) => {
    e.stopPropagation();
    const dishId = dish.id || dish._id;
    if (isFavorite(dishId)) {
      removeFavorite(dishId);
      showNotification("Removed from favorites");
    } else {
      addFavorite(dish);
      showNotification("Added to favorites");
    }
  };

  const showNotification = (message) => {
    setNotification({ show: true, message });
    setTimeout(() => {
      setNotification({ show: false, message: "" });
    }, 3000);
  };

  const handleAddToMealPlan = (meal) => {
    // Navigate to meal plan page with the selected meal
    navigate('/meal-plan', { state: { selectedMeal: meal, openMealSelector: true } });
    setShowModal(false);
    showNotification("Redirected to meal planner");
  };

  if (isLoading) {
    return <div className="sidebar-loading">Loading...</div>;
  }

  return (
    <div className={`sidebar ${isActive ? 'active' : ''}`}>
      {/* User Info */}
      {user && (
        <div className="sidebar-user">
          <div className="sidebar-user-name">{user.name}</div>
          <div className="sidebar-user-email">{user.email}</div>
        </div>
      )}
      
      {/* Conditional Navigation based on Admin Status and View Mode */}
      {isAdmin && !isViewingAsUser ? (
        // Admin View Navigation
        <>
          <div className="sidebar-section">
            <div className="sidebar-section-title">Admin Menu</div>
            <ul className="sidebar-nav">
              <li className={`sidebar-nav-item ${isActiveLink('/admin') ? 'active' : ''}`}>
                <Link to="/admin">
                  Admin Dashboard
                </Link>
              </li>
              <li className={`sidebar-nav-item ${isActiveLink('/feedback-management') ? 'active' : ''}`}>
                <Link to="/feedback-management">
                  Feedback Management
                </Link>
              </li>
            </ul>
          </div>

          <div className="sidebar-section">
            <div className="sidebar-section-title">Account</div>
            <ul className="sidebar-nav">
              <li className={`sidebar-nav-item ${isActiveLink('/profile') ? 'active' : ''}`}>
                <Link to="/profile">
                  Profile
                </Link>
              </li>
            </ul>
          </div>
        </>
      ) : (
        // User View Navigation (for regular users or admin viewing as user)
        <>
          <div className="sidebar-section">
            <div className="sidebar-section-title">Main Menu</div>
            <ul className="sidebar-nav">
              <li className={`sidebar-nav-item ${isActiveLink('/home') ? 'active' : ''}`}>
                <Link to="/home">
                  Home
                </Link>
              </li>
              <li className={`sidebar-nav-item ${isActiveLink('/history') ? 'active' : ''}`}>
                <Link to="/history">
                  History
                </Link>
              </li>
            </ul>
          </div>

          <div className="sidebar-section">
            <div className="sidebar-section-title">User Menu</div>
            <ul className="sidebar-nav">
              <li className={`sidebar-nav-item ${isActiveLink('/profile') ? 'active' : ''}`}>
                <Link to="/profile">
                  Profile
                </Link>
              </li>
              <li className={`sidebar-nav-item ${isActiveLink('/favorites') ? 'active' : ''}`}>
                <Link to="/favorites">
                  Favorites
                </Link>
              </li>
              <li className={`sidebar-nav-item ${isActiveLink('/meal-plan') ? 'active' : ''}`}>
                <Link to="/meal-plan">
                  Meal Plan
                </Link>
              </li>
              <li className={`sidebar-nav-item ${isActiveLink('/family') ? 'active' : ''}`}>
                <Link to="/family">
                  Family Profile
                </Link>
              </li>
              <li className={`sidebar-nav-item ${isActiveLink('/help-center') ? 'active' : ''}`}>
                <Link to="/help-center">
                  Help Center
                </Link>
              </li>
            </ul>
          </div>
        </>
      )}

      {/* Recommendations Section - Only show in user view */}
      {(!isAdmin || isViewingAsUser) && (
        <div className="sidebar-section">
          <div className="sidebar-section-title">Recommendations</div>
          <div className="recommendations-container">
          {recommendationsLoading ? (
            <div className="recommendations-loading">
              <div className="loading-spinner"></div>
              <span>Loading recommendations...</span>
            </div>
          ) : recommendations.length > 0 ? (
            <div className="recommendations-list">
              {recommendations.map((meal, index) => (
                <div
                  key={meal._id || index}
                  className="recommendation-item"
                  onClick={() => handleRecommendationClick(meal)}
                >
                  <div className="recommendation-image">
                    {meal.image ? (
                      <img src={meal.image} alt={meal.name} />
                    ) : (
                      <div className="recommendation-placeholder">
                        🍽️
                      </div>
                    )}
                  </div>
                  <div className="recommendation-content">
                    <div className="recommendation-name">{meal.name}</div>
                    <div className="recommendation-meta">
                      <span className="recommendation-type">{meal.category}</span>
                      {meal.calories && (
                        <span className="recommendation-calories">{meal.calories} cal</span>
                      )}
                      {meal.prepTime && (
                        <span className="recommendation-time">{meal.prepTime} min</span>
                      )}
                    </div>
                    {meal.description && (
                      <div className="recommendation-description">
                        {meal.description.length > 60
                          ? `${meal.description.substring(0, 60)}...`
                          : meal.description}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              <div className="view-all-recommendations">
                <Link to="/meals" className="view-all-link">
                  View all meals →
                </Link>
              </div>
            </div>
          ) : (
            <div className="no-recommendations">
              <div className="no-recommendations-icon">🍽️</div>
              <div className="no-recommendations-text">
                No recommendations available
              </div>
              <Link to="/meals" className="browse-meals-link">
                Browse meals
              </Link>
            </div>
          )}
        </div>
        </div>
      )}

      {/* Notification */}
      {notification.show && (
        <div className="notification">
          {notification.message}
        </div>
      )}

      {/* Meal Details Modal */}
      {showModal && selectedMeal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{selectedMeal.name}</h2>
              <button className="close-modal" onClick={closeMealDetails}>
                <FaTimes />
              </button>
            </div>
            <div className="modal-body">
              <div className="meal-image">
                <img src={selectedMeal.image} alt={selectedMeal.name} />
              </div>
              <div className="meal-details">
                <p className="meal-description">
                  {selectedMeal.description}
                </p>
                <div className="meal-meta">
                  <span className="meal-rating">
                    {selectedMeal.rating} &#9733;
                  </span>
                  <span className="meal-category">
                    {selectedMeal.category}
                  </span>
                  <span className="meal-price">
                    Calories: {selectedMeal.calories} (
                    {selectedMeal.priceRange ? getPesoSigns(selectedMeal.priceRange) : "₱"})
                  </span>
                </div>
                {selectedMeal.ingredients && selectedMeal.ingredients.length > 0 && (
                  <div className="meal-ingredients">
                    <h3>Ingredients</h3>
                    <ul className="ingredients-list">
                      {selectedMeal.ingredients.map((ingredient, idx) => (
                        <li key={idx}>{ingredient}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {(selectedMeal.protein || selectedMeal.carbs || selectedMeal.fat || selectedMeal.prepTime) && (
                  <div className="meal-nutrition">
                    <h3>Nutrition Information</h3>
                    <div className="nutrition-grid">
                      {selectedMeal.protein && (
                        <div className="nutrition-item">
                          <span className="nutrition-label">Protein:</span>
                          <span className="nutrition-value">
                            {selectedMeal.protein}g
                          </span>
                        </div>
                      )}
                      {selectedMeal.carbs && (
                        <div className="nutrition-item">
                          <span className="nutrition-label">Carbs:</span>
                          <span className="nutrition-value">
                            {selectedMeal.carbs}g
                          </span>
                        </div>
                      )}
                      {selectedMeal.fat && (
                        <div className="nutrition-item">
                          <span className="nutrition-label">Fat:</span>
                          <span className="nutrition-value">
                            {selectedMeal.fat}g
                          </span>
                        </div>
                      )}
                      {selectedMeal.prepTime && (
                        <div className="nutrition-item">
                          <span className="nutrition-label">Prep Time:</span>
                          <span className="nutrition-value">
                            {selectedMeal.prepTime} mins
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
                {selectedMeal.instructions && selectedMeal.instructions.length > 0 && (
                  <div className="meal-steps">
                    <h3>Cooking Instructions</h3>
                    <ol>
                      {selectedMeal.instructions.slice(0, 3).map((step, idx) => (
                        <li key={idx}>{step}</li>
                      ))}
                      {selectedMeal.instructions.length > 3 && (
                        <li>... and {selectedMeal.instructions.length - 3} more steps</li>
                      )}
                    </ol>
                  </div>
                )}
                {selectedMeal.dietaryTags && selectedMeal.dietaryTags.length > 0 && (
                  <div className="meal-tags">
                    <h3>Dietary Tags</h3>
                    <div className="tags-container">
                      {selectedMeal.dietaryTags.map((tag, idx) => (
                        <span key={idx} className="dietary-tag">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                {selectedMeal.mealType && selectedMeal.mealType.length > 0 && (
                  <div className="meal-types">
                    <h3>Meal Types</h3>
                    <div className="tags-container">
                      {selectedMeal.mealType.map((type, idx) => (
                        <span key={idx} className="meal-type-tag">
                          {type}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="favorite-modal-btn"
                onClick={(e) => toggleFavorite(e, selectedMeal)}
              >
                {isFavorite(selectedMeal.id || selectedMeal._id) ? (
                  <>
                    Remove from Favorites <FaHeart />
                  </>
                ) : (
                  <>
                    Add to Favorites <FaRegHeart />
                  </>
                )}
              </button>
              <button
                className="meal-plan-modal-btn"
                onClick={() => handleAddToMealPlan(selectedMeal)}
              >
                Add to Meal Plan <FaCalendarPlus />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;

