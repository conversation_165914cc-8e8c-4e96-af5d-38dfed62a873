.activity-log {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.activity-log h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
}

.log-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-container select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.search-container {
  display: flex;
  gap: 0.5rem;
}

.search-container input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
}

.search-container button {
  padding: 0.5rem 1rem;
  background-color: #4CAF50; /* Match your app's primary color */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.search-container button:hover {
  background-color: #45a049;
}

.activity-table {
  width: 100%;
  border-collapse: collapse;
}

.activity-table th,
.activity-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.activity-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #333;
}

.activity-table tr:hover {
  background-color: #f9f9f9;
}

.activity-type {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
}

.activity-type.login {
  background-color: #e3f2fd;
  color: #1976d2;
}

.activity-type.signup {
  background-color: #e8f5e9;
  color: #388e3c;
}

.activity-type.create_meal_plan {
  background-color: #fff8e1;
  color: #ffa000;
}

.activity-type.update_profile {
  background-color: #e0f2f1;
  color: #00897b;
}

.activity-type.delete_meal_plan {
  background-color: #ffebee;
  color: #d32f2f;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1.5rem;
  gap: 1rem;
}

.pagination button {
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.pagination button:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.pagination button:disabled {
  color: #aaa;
  cursor: not-allowed;
}

.page-info {
  color: #666;
}

.no-activities {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

.loading-log, .error-log {
  text-align: center;
  padding: 2rem;
}

.error-log {
  color: #f44336;
}

@media (max-width: 768px) {
  .log-controls {
    flex-direction: column;
  }
  
  .search-container {
    width: 100%;
  }
  
  .search-container input {
    flex: 1;
  }
  
  .activity-table {
    font-size: 0.9rem;
  }
  
  .activity-table th,
  .activity-table td {
    padding: 0.5rem;
  }
}

