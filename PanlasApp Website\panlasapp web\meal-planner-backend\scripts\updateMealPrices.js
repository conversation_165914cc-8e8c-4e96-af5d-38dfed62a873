const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '../.env' });

// Import the Meal model
const Meal = require('../models/Meal');

// Read the updated Filipino meal data with prices
const filePath = path.join(__dirname, 'filipinoMealData.json');
const mealsData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

async function updateMealPrices() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    console.log('📊 Starting price update for meals...');
    
    let updatedCount = 0;
    let notFoundCount = 0;

    for (const mealData of mealsData) {
      try {
        // Find meal by name and update price
        const result = await Meal.updateOne(
          { name: mealData.name },
          { 
            $set: { 
              price: mealData.price 
            }
          }
        );

        if (result.matchedCount > 0) {
          console.log(`✅ Updated ${mealData.name}: ₱${mealData.price}`);
          updatedCount++;
        } else {
          console.log(`❌ Meal not found in database: ${mealData.name}`);
          notFoundCount++;
        }
      } catch (error) {
        console.error(`❌ Error updating ${mealData.name}:`, error.message);
      }
    }

    console.log('\n📈 Update Summary:');
    console.log(`   ✅ Successfully updated: ${updatedCount} meals`);
    console.log(`   ❌ Not found in database: ${notFoundCount} meals`);
    console.log(`   📊 Total processed: ${mealsData.length} meals`);

    // Verify the updates by fetching a few meals
    console.log('\n🔍 Verifying updates...');
    const sampleMeals = await Meal.find({ price: { $exists: true } }).limit(5);
    
    console.log('📋 Sample meals with prices:');
    sampleMeals.forEach(meal => {
      console.log(`   ${meal.name}: ₱${meal.price}`);
    });

    // Check if any meals are missing prices
    const mealsWithoutPrice = await Meal.countDocuments({ 
      $or: [
        { price: { $exists: false } },
        { price: null },
        { price: undefined }
      ]
    });

    if (mealsWithoutPrice > 0) {
      console.log(`\n⚠️  Warning: ${mealsWithoutPrice} meals still don't have prices`);
    } else {
      console.log('\n🎉 All meals now have price information!');
    }

  } catch (error) {
    console.error('❌ Error updating meal prices:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the update
updateMealPrices();
