const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Test disable/enable user functionality
const testDisableUser = async () => {
  try {
    console.log('🧪 Testing Disable/Enable User Functionality...\n');

    // Step 1: Login as admin
    console.log('1. Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
      email: '<EMAIL>',
      password: '132fast@!'
    });

    if (!loginResponse.data.token) {
      throw new Error('Login failed - no token received');
    }

    const token = loginResponse.data.token;
    console.log('✅ Login successful');
    console.log('User:', loginResponse.data.user);

    const config = {
      headers: {
        'x-auth-token': token
      }
    };

    // Step 2: Get all users
    console.log('\n2. Getting all users...');
    const usersResponse = await axios.get(`${BASE_URL}/admin/users`, config);
    const users = usersResponse.data;
    console.log(`✅ Found ${users.length} users`);

    // Find a non-admin user to test with
    const testUser = users.find(user => !user.isAdmin && user.username !== 'umer');
    if (!testUser) {
      console.log('❌ No non-admin user found to test with');
      return;
    }

    console.log(`📝 Testing with user: ${testUser.username} (${testUser.email})`);
    console.log(`   Current status: isActive = ${testUser.isActive}`);

    // Step 3: Test disable user
    console.log('\n3. Testing disable user...');
    console.log(`Making request to: ${BASE_URL}/admin/users/${testUser._id}/disable`);
    console.log('Headers:', config.headers);
    try {
      const disableResponse = await axios.put(
        `${BASE_URL}/admin/users/${testUser._id}/disable`,
        {},
        config
      );
      console.log('✅ Disable request successful');
      console.log('Response:', disableResponse.data);
    } catch (disableError) {
      console.log('❌ Disable request failed');
      console.log('Error status:', disableError.response?.status);
      console.log('Error message:', disableError.response?.data?.message);
      console.log('Error data:', disableError.response?.data);
      console.log('Full error:', disableError.message);
      console.log('Request URL:', disableError.config?.url);
      return;
    }

    // Step 4: Verify user is disabled
    console.log('\n4. Verifying user is disabled...');
    const usersAfterDisable = await axios.get(`${BASE_URL}/admin/users`, config);
    const disabledUser = usersAfterDisable.data.find(user => user._id === testUser._id);
    console.log(`User status after disable: isActive = ${disabledUser.isActive}`);

    if (!disabledUser.isActive) {
      console.log('✅ User successfully disabled');
    } else {
      console.log('❌ User was not disabled');
    }

    // Step 5: Test enable user
    console.log('\n5. Testing enable user...');
    try {
      const enableResponse = await axios.put(
        `${BASE_URL}/admin/users/${testUser._id}/enable`,
        {},
        config
      );
      console.log('✅ Enable request successful');
      console.log('Response:', enableResponse.data);
    } catch (enableError) {
      console.log('❌ Enable request failed');
      console.log('Error status:', enableError.response?.status);
      console.log('Error message:', enableError.response?.data?.message);
      console.log('Full error:', enableError.message);
      return;
    }

    // Step 6: Verify user is enabled
    console.log('\n6. Verifying user is enabled...');
    const usersAfterEnable = await axios.get(`${BASE_URL}/admin/users`, config);
    const enabledUser = usersAfterEnable.data.find(user => user._id === testUser._id);
    console.log(`User status after enable: isActive = ${enabledUser.isActive}`);

    if (enabledUser.isActive) {
      console.log('✅ User successfully enabled');
    } else {
      console.log('❌ User was not enabled');
    }

    console.log('\n🎉 Disable/Enable functionality test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
};

// Run the test
testDisableUser();
