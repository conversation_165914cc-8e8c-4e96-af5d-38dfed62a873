import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './RecentActivity.css';

function RecentActivity() {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchRecentActivity = async () => {
      try {
        const token = localStorage.getItem('token');
        
        const config = {
          headers: {
            'x-auth-token': token
          }
        };

        const response = await axios.get('http://localhost:5000/api/activity/recent', config);
        setActivities(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to load recent activity');
        console.error('Recent activity error:', err);
        setLoading(false);
      }
    };

    fetchRecentActivity();
  }, []);

  const getActivityIcon = (type) => {
    switch (type) {
      case 'login':
        return '🔑';
      case 'signup':
        return '👤';
      case 'create_meal_plan':
        return '🍽️';
      case 'update_profile':
        return '✏️';
      case 'delete_meal_plan':
        return '🗑️';
      default:
        return '📝';
    }
  };

  if (loading) return <div className="loading-activity">Loading recent activity...</div>;
  if (error) return <div className="error-activity">{error}</div>;

  return (
    <div className="recent-activity">
      <h2>Recent Activity</h2>
      
      {activities.length === 0 ? (
        <div className="no-activity">No recent activity to display</div>
      ) : (
        <ul className="activity-list">
          {activities.map((activity, index) => (
            <li key={index} className="activity-item">
              <div className="activity-icon">
                {getActivityIcon(activity.type)}
              </div>
              <div className="activity-content">
                <div className="activity-user">{activity.username}</div>
                <div className="activity-description">{activity.description}</div>
                <div className="activity-time">{new Date(activity.timestamp).toLocaleString()}</div>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default RecentActivity;
