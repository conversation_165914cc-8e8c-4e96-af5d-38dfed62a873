import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../context/AuthContext';
import { colors, legacyFonts, legacySpacing, borderRadius } from '../../styles/theme';
import PasswordStrengthIndicator from '../../components/PasswordStrengthIndicator';
import EmailValidator from '../../components/EmailValidator';

const RegisterScreen = ({ navigation }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    dateOfBirth: '',
    gender: '',
    barangay: '',
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showGenderPicker, setShowGenderPicker] = useState(false);
  const [showEmailValidation, setShowEmailValidation] = useState(false);

  const { register } = useAuth();

  // Gender options that match backend enum values
  const genderOptions = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
    { label: 'Prefer not to say', value: 'prefer not to say' },
  ];

  const handleRegister = async () => {
    // Validation
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    if (formData.password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters long');
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    setLoading(true);
    try {
      const result = await register({
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
        username: formData.username.trim() || formData.email.trim(),
        dateOfBirth: formData.dateOfBirth,
        gender: formData.gender,
        barangay: formData.barangay.trim(),
      });

      if (result.success) {
        if (result.data.requiresVerification) {
          Alert.alert(
            'Registration Successful!',
            'Please check your email for a verification code to complete your registration.',
            [
              {
                text: 'OK',
                onPress: () => navigation.navigate('OTPVerification', {
                  email: formData.email.trim().toLowerCase()
                })
              }
            ]
          );
        } else {
          Alert.alert('Registration Successful!', 'Welcome to PanlasApp!');
        }
      } else {
        // Check if user already exists
        if (result.userExists || result.redirectToLogin) {
          Alert.alert(
            'Account Already Exists',
            result.error,
            [
              {
                text: 'Cancel',
                style: 'cancel'
              },
              {
                text: 'Login',
                onPress: () => {
                  // Pre-fill the email if it was the email that already exists
                  const emailToFill = result.existingEmail || formData.email;
                  navigation.navigate('Login', {
                    prefillEmail: emailToFill
                  });
                }
              }
            ]
          );
        } else {
          Alert.alert('Registration Failed', result.error);
        }
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Account</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.welcomeSection}>
          <Text style={styles.title}>Join Panlas!</Text>
          <Text style={styles.subtitle}>Create your account to start planning delicious Filipino meals</Text>
        </View>

        <View style={styles.formSection}>
          {/* Name Fields */}
          <View style={styles.row}>
            <View style={[styles.inputContainer, styles.halfWidth]}>
              <Text style={styles.inputLabel}>First Name *</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter first name"
                value={formData.firstName}
                onChangeText={(text) => updateFormData('firstName', text)}
                autoCapitalize="words"
              />
            </View>
            <View style={[styles.inputContainer, styles.halfWidth]}>
              <Text style={styles.inputLabel}>Last Name *</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter last name"
                value={formData.lastName}
                onChangeText={(text) => updateFormData('lastName', text)}
                autoCapitalize="words"
              />
            </View>
          </View>

          {/* Email */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email Address *</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your email"
              value={formData.email}
              onChangeText={(text) => {
                updateFormData('email', text);
                setShowEmailValidation(text.length > 0);
              }}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
            <EmailValidator
              email={formData.email}
              showValidation={showEmailValidation}
            />
          </View>

          {/* Username */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Username</Text>
            <TextInput
              style={styles.input}
              placeholder="Choose a username (optional)"
              value={formData.username}
              onChangeText={(text) => updateFormData('username', text)}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          {/* Password */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Password *</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Create a password"
                value={formData.password}
                onChangeText={(text) => updateFormData('password', text)}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Ionicons
                  name={showPassword ? "eye-off" : "eye"}
                  size={20}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
            <PasswordStrengthIndicator password={formData.password} />
          </View>

          {/* Confirm Password */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Confirm Password *</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChangeText={(text) => updateFormData('confirmPassword', text)}
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                <Ionicons
                  name={showConfirmPassword ? "eye-off" : "eye"}
                  size={20}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Optional Fields */}
          <View style={styles.optionalSection}>
            <Text style={styles.sectionTitle}>Additional Information (Optional)</Text>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Date of Birth</Text>
              <TextInput
                style={styles.input}
                placeholder="YYYY-MM-DD"
                value={formData.dateOfBirth}
                onChangeText={(text) => updateFormData('dateOfBirth', text)}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Gender</Text>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => setShowGenderPicker(true)}
              >
                <Text style={[styles.pickerText, !formData.gender && styles.placeholderText]}>
                  {formData.gender ? genderOptions.find(option => option.value === formData.gender)?.label : 'Select Gender'}
                </Text>
                <Ionicons name="chevron-down" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Barangay</Text>
              <TextInput
                style={styles.input}
                placeholder="Your barangay"
                value={formData.barangay}
                onChangeText={(text) => updateFormData('barangay', text)}
              />
            </View>
          </View>

          {/* Register Button */}
          <TouchableOpacity
            style={[styles.registerButton, loading && styles.disabledButton]}
            onPress={handleRegister}
            disabled={loading}
          >
            <Text style={styles.registerButtonText}>
              {loading ? 'Creating Account...' : 'Create Account'}
            </Text>
          </TouchableOpacity>

          {/* Login Link */}
          <View style={styles.loginSection}>
            <Text style={styles.loginText}>Already have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={styles.loginLink}>Sign In</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Gender Picker Modal */}
      <Modal
        visible={showGenderPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowGenderPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Gender</Text>
              <TouchableOpacity
                onPress={() => setShowGenderPicker(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name="close" size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            {genderOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.modalOption,
                  formData.gender === option.value && styles.selectedOption
                ]}
                onPress={() => {
                  updateFormData('gender', option.value);
                  setShowGenderPicker(false);
                }}
              >
                <Text style={[
                  styles.modalOptionText,
                  formData.gender === option.value && styles.selectedOptionText
                ]}>
                  {option.label}
                </Text>
                {formData.gender === option.value && (
                  <Ionicons name="checkmark" size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: legacySpacing.sm,
  },
  headerTitle: {
    fontSize: legacyFonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: legacySpacing.lg,
  },
  welcomeSection: {
    alignItems: 'center',
    marginBottom: legacySpacing.xl,
  },
  title: {
    fontSize: legacyFonts.sizes.xxlarge,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: legacySpacing.sm,
  },
  subtitle: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  formSection: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inputContainer: {
    marginBottom: legacySpacing.md,
  },
  halfWidth: {
    width: '48%',
  },
  inputLabel: {
    fontSize: legacyFonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    marginBottom: legacySpacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    fontSize: legacyFonts.sizes.medium,
    backgroundColor: colors.surface,
    color: colors.text,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    backgroundColor: colors.surface,
  },
  passwordInput: {
    flex: 1,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    fontSize: legacyFonts.sizes.medium,
    color: colors.text,
  },
  eyeButton: {
    padding: legacySpacing.md,
  },
  optionalSection: {
    marginTop: legacySpacing.lg,
    marginBottom: legacySpacing.lg,
  },
  sectionTitle: {
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: legacySpacing.md,
  },
  registerButton: {
    backgroundColor: colors.primary,
    paddingVertical: legacySpacing.md,
    borderRadius: borderRadius.medium,
    alignItems: 'center',
    marginTop: legacySpacing.lg,
  },
  registerButtonText: {
    color: colors.surface,
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
  loginSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: legacySpacing.lg,
    marginBottom: legacySpacing.xl,
  },
  loginText: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
  },
  loginLink: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.primary,
    fontWeight: 'bold',
  },
  // Picker styles
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    backgroundColor: colors.surface,
  },
  pickerText: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.text,
  },
  placeholderText: {
    color: colors.textSecondary,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.large,
    padding: legacySpacing.lg,
    width: '80%',
    maxWidth: 300,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: legacySpacing.md,
  },
  modalTitle: {
    fontSize: legacyFonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  modalCloseButton: {
    padding: legacySpacing.sm,
  },
  modalOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.sm,
    borderRadius: borderRadius.medium,
  },
  selectedOption: {
    backgroundColor: colors.primaryLight || colors.primary + '20',
  },
  modalOptionText: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.text,
  },
  selectedOptionText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
});

export default RegisterScreen;
