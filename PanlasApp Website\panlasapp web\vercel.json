{"version": 2, "framework": "vite", "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install", "routes": [{"src": "/api/(.*)", "dest": "/api/$1", "headers": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS", "Access-Control-Allow-Headers": "X-Requested-With, Content-Type, Authorization"}}, {"src": "/imagesfood/(.*)", "dest": "/imagesfood/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, PATCH, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "X-Requested-With, Content-Type, Authorization"}]}], "env": {"VITE_API_URL": "@vite_api_url"}, "build": {"env": {"VITE_API_URL": "@vite_api_url"}}}