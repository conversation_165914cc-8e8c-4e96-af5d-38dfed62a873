import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useResponsiveScreen, getResponsiveFontSize, getResponsiveSpacing, getResponsiveDimensions } from '../utils/responsive';
import { colors } from '../styles/theme';

const ResponsiveTest = () => {
  const { size: screenSize, width, height } = useResponsiveScreen();
  const dimensions = getResponsiveDimensions(screenSize);

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { fontSize: getResponsiveFontSize('large', screenSize) }]}>
        Responsive Test
      </Text>
      
      <View style={[styles.infoCard, { 
        padding: getResponsiveSpacing('md', screenSize),
        borderRadius: dimensions.borderRadius 
      }]}>
        <Text style={[styles.infoText, { fontSize: getResponsiveFontSize('medium', screenSize) }]}>
          Screen Size: {screenSize}
        </Text>
        <Text style={[styles.infoText, { fontSize: getResponsiveFontSize('medium', screenSize) }]}>
          Width: {width}px
        </Text>
        <Text style={[styles.infoText, { fontSize: getResponsiveFontSize('medium', screenSize) }]}>
          Height: {height}px
        </Text>
        <Text style={[styles.infoText, { fontSize: getResponsiveFontSize('medium', screenSize) }]}>
          Tab Bar Height: {dimensions.tabBarHeight}px
        </Text>
        <Text style={[styles.infoText, { fontSize: getResponsiveFontSize('medium', screenSize) }]}>
          Icon Size: {dimensions.iconSize}px
        </Text>
        <Text style={[styles.infoText, { fontSize: getResponsiveFontSize('medium', screenSize) }]}>
          Button Height: {dimensions.buttonHeight}px
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 20,
  },
  infoCard: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    minWidth: 250,
  },
  infoText: {
    color: colors.text,
    marginBottom: 8,
  },
});

export default ResponsiveTest;
