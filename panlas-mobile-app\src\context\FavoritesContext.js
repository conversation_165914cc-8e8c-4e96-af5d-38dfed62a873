import React, { createContext, useState, useContext, useEffect } from 'react';
import { userAPI } from '../services/api';
import { useAuth } from './AuthContext';

const FavoritesContext = createContext();

export const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
};

export const FavoritesProvider = ({ children }) => {
  const [favorites, setFavorites] = useState([]);
  const [favoriteMealPlans, setFavoriteMealPlans] = useState([]);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated } = useAuth();

  // Ensure favorites is always an array
  const safeFavorites = Array.isArray(favorites) ? favorites : [];
  const safeFavoriteMealPlans = Array.isArray(favoriteMealPlans) ? favoriteMealPlans : [];

  // Load favorites when user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      loadFavorites();
      loadFavoriteMealPlans();
    } else {
      setFavorites([]);
      setFavoriteMealPlans([]);
    }
  }, [isAuthenticated]);

  const loadFavorites = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getFavoriteMeals();
      console.log('Favorites API response:', response);

      // Handle different response structures
      let favoritesData = [];
      if (response.data) {
        if (Array.isArray(response.data)) {
          favoritesData = response.data;
        } else if (response.data.favoriteMeals) {
          favoritesData = response.data.favoriteMeals;
        }
      }

      console.log('Setting favorites:', favoritesData);
      setFavorites(Array.isArray(favoritesData) ? favoritesData : []);
    } catch (error) {
      console.error('Error loading favorites:', error);
      setFavorites([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  const addFavorite = async (meal) => {
    try {
      console.log('Adding favorite meal:', meal);
      const response = await userAPI.addFavoriteMeal({ meal });
      console.log('Add favorite API response:', response);

      // Update local state - either use server response or add locally
      if (response.data && response.data.favoriteMeals) {
        setFavorites(response.data.favoriteMeals);
      } else {
        setFavorites(prev => {
          const currentFavorites = Array.isArray(prev) ? prev : [];
          return [...currentFavorites, meal];
        });
      }

      console.log('Successfully added favorite');
      return { success: true };
    } catch (error) {
      console.error('Error adding favorite:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to add favorite' };
    }
  };

  const removeFavorite = async (mealId) => {
    try {
      console.log('Removing favorite meal ID:', mealId);
      const response = await userAPI.removeFavoriteMeal(mealId);
      console.log('Remove favorite API response:', response);

      // Update local state - either use server response or remove locally
      if (response.data && response.data.favoriteMeals) {
        setFavorites(response.data.favoriteMeals);
      } else {
        setFavorites(prev => {
          const currentFavorites = Array.isArray(prev) ? prev : [];
          return currentFavorites.filter(meal => (meal.id || meal._id) !== mealId);
        });
      }

      console.log('Successfully removed favorite');
      return { success: true };
    } catch (error) {
      console.error('Error removing favorite:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to remove favorite' };
    }
  };

  const isFavorite = (mealId) => {
    return safeFavorites.some(meal => (meal.id || meal._id) === mealId);
  };

  // Load favorite meal plans
  const loadFavoriteMealPlans = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getFavoriteMealPlans();
      console.log('Favorite meal plans API response:', response);

      // Handle different response structures
      let favoritePlansData = [];
      if (response.data) {
        if (Array.isArray(response.data)) {
          favoritePlansData = response.data;
        } else if (response.data.favoriteMealPlans) {
          favoritePlansData = response.data.favoriteMealPlans;
        }
      }

      console.log('Setting favorite meal plans:', favoritePlansData);
      setFavoriteMealPlans(Array.isArray(favoritePlansData) ? favoritePlansData : []);
    } catch (error) {
      console.error('Error loading favorite meal plans:', error);
      setFavoriteMealPlans([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  // Add meal plan to favorites
  const addFavoriteMealPlan = async (mealPlan) => {
    try {
      console.log('Adding favorite meal plan:', mealPlan);
      const response = await userAPI.addFavoriteMealPlan(mealPlan);
      console.log('Add favorite meal plan API response:', response);

      // Update local state
      if (response.data && response.data.favoriteMealPlans) {
        setFavoriteMealPlans(response.data.favoriteMealPlans);
      } else {
        setFavoriteMealPlans(prev => {
          const currentFavorites = Array.isArray(prev) ? prev : [];
          return [...currentFavorites, mealPlan];
        });
      }

      console.log('Successfully added favorite meal plan');
      return { success: true };
    } catch (error) {
      console.error('Error adding favorite meal plan:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to add favorite meal plan' };
    }
  };

  // Remove meal plan from favorites
  const removeFavoriteMealPlan = async (planId) => {
    try {
      console.log('Removing favorite meal plan:', planId);
      const response = await userAPI.removeFavoriteMealPlan(planId);
      console.log('Remove favorite meal plan API response:', response);

      // Update local state
      if (response.data && response.data.favoriteMealPlans) {
        setFavoriteMealPlans(response.data.favoriteMealPlans);
      } else {
        setFavoriteMealPlans(prev => {
          const currentFavorites = Array.isArray(prev) ? prev : [];
          return currentFavorites.filter(plan => (plan._id || plan.id) !== planId);
        });
      }

      console.log('Successfully removed favorite meal plan');
      return { success: true };
    } catch (error) {
      console.error('Error removing favorite meal plan:', error);
      return { success: false, error: error.response?.data?.message || 'Failed to remove favorite meal plan' };
    }
  };

  // Check if meal plan is favorite
  const isFavoriteMealPlan = (planId) => {
    return safeFavoriteMealPlans.some(plan => (plan.plan?._id || plan.plan?.id || plan._id || plan.id) === planId);
  };

  const value = {
    favorites: safeFavorites,
    favoriteMealPlans: safeFavoriteMealPlans,
    loading,
    addFavorite,
    removeFavorite,
    isFavorite,
    loadFavorites,
    addFavoriteMealPlan,
    removeFavoriteMealPlan,
    isFavoriteMealPlan,
    loadFavoriteMealPlans,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};
