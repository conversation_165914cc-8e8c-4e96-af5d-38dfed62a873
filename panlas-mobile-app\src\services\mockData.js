// Mock data for development when backend is not available
const IMAGE_BASE_URL = 'http://*************:5000';

export const mockMeals = [
  {
    id: 1,
    name: "<PERSON>ob<PERSON>",
    description: "Classic Filipino dish with pork or chicken marinated in vinegar, soy sauce, and garlic",
    image: `${IMAGE_BASE_URL}/imagesfood/adobo_manok.jpg`,
    category: ["Meat"],
    mealType: ["lunch", "dinner"],
    calories: 350,
    cookingTime: "45 minutes",
    difficulty: "Easy",
    ingredients: ["Pork", "Soy sauce", "Vinegar", "Garlic", "Bay leaves"],
    rating: 4.8,
    tags: ["Filipino", "Traditional", "Comfort Food"]
  },
  {
    id: 2,
    name: "Sinigang",
    description: "Sour soup with tamarind base, vegetables, and choice of meat or seafood",
    image: `${IMAGE_BASE_URL}/imagesfood/Pork_sinigang.jpg`,
    category: ["Soup"],
    mealType: ["lunch", "dinner"],
    calories: 280,
    cookingTime: "60 minutes",
    difficulty: "Medium",
    ingredients: ["Pork ribs", "Tamarind", "Kangkong", "Radish", "Tomatoes"],
    rating: 4.7,
    tags: ["Filipino", "Soup", "Healthy"]
  },
  {
    id: 3,
    name: "Lechon Kawali",
    description: "Crispy deep-fried pork belly served with liver sauce",
    image: `${IMAGE_BASE_URL}/imagesfood/Lechon_Kawali.jpg`,
    category: ["Meat"],
    mealType: ["lunch", "dinner"],
    calories: 520,
    cookingTime: "90 minutes",
    difficulty: "Hard",
    ingredients: ["Pork belly", "Salt", "Pepper", "Bay leaves", "Oil"],
    rating: 4.9,
    tags: ["Filipino", "Crispy", "Festive"]
  },
  {
    id: 4,
    name: "Pancit Canton",
    description: "Stir-fried noodles with vegetables and meat or seafood",
    image: `${IMAGE_BASE_URL}/imagesfood/Filipino_Pancit.jpg`,
    category: ["Noodles"],
    mealType: ["lunch", "dinner"],
    calories: 320,
    cookingTime: "30 minutes",
    difficulty: "Easy",
    ingredients: ["Canton noodles", "Cabbage", "Carrots", "Pork", "Shrimp"],
    rating: 4.6,
    tags: ["Filipino", "Noodles", "Quick"]
  },
  {
    id: 5,
    name: "Kare-Kare",
    description: "Rich peanut stew with oxtail and vegetables",
    image: `${IMAGE_BASE_URL}/imagesfood/kare_kare.jpg`,
    category: ["Meat"],
    mealType: ["lunch", "dinner"],
    calories: 450,
    cookingTime: "120 minutes",
    difficulty: "Hard",
    ingredients: ["Oxtail", "Peanut butter", "Eggplant", "String beans", "Banana heart"],
    rating: 4.8,
    tags: ["Filipino", "Traditional", "Rich"]
  },
  {
    id: 6,
    name: "Lumpia Shanghai",
    description: "Crispy spring rolls filled with ground pork and vegetables",
    image: `${IMAGE_BASE_URL}/imagesfood/Lumpia_Shanghai.jpg`,
    category: ["Appetizer"],
    mealType: ["snack"],
    calories: 180,
    cookingTime: "45 minutes",
    difficulty: "Medium",
    ingredients: ["Spring roll wrapper", "Ground pork", "Carrots", "Onions", "Oil"],
    rating: 4.7,
    tags: ["Filipino", "Appetizer", "Crispy"]
  },
  {
    id: 7,
    name: "Chicken Tinola",
    description: "Clear soup with chicken, green papaya, and chili leaves",
    image: `${IMAGE_BASE_URL}/imagesfood/Chicken_tinola.jpg`,
    category: ["Soup"],
    mealType: ["lunch", "dinner"],
    calories: 250,
    cookingTime: "50 minutes",
    difficulty: "Easy",
    ingredients: ["Chicken", "Green papaya", "Ginger", "Chili leaves", "Fish sauce"],
    rating: 4.5,
    tags: ["Filipino", "Soup", "Healthy", "Comfort"]
  },
  {
    id: 8,
    name: "Champorado",
    description: "Sweet chocolate rice porridge, perfect for breakfast",
    image: `${IMAGE_BASE_URL}/imagesfood/champorado.jpg`,
    category: ["Rice Cake"],
    mealType: ["breakfast"],
    calories: 350,
    cookingTime: "30 minutes",
    difficulty: "Easy",
    ingredients: ["Glutinous rice", "Cocoa tablets", "Sugar", "Milk"],
    rating: 4.6,
    tags: ["Filipino", "Sweet", "Breakfast"]
  }
];

export const mockFamilyMembers = [
  {
    _id: "1",
    name: "Maria Santos",
    dietaryPreferences: {
      restrictions: ["Vegetarian"],
      allergies: ["Nuts"],
      calorieTarget: "1800"
    }
  },
  {
    _id: "2", 
    name: "Juan Santos",
    dietaryPreferences: {
      restrictions: ["Low-Carb"],
      allergies: [],
      calorieTarget: "2200"
    }
  }
];

export const mockRecentlyViewed = [
  mockMeals[0],
  mockMeals[2],
  mockMeals[4]
];

export const mockRecentlyAddedToMealPlans = [
  {
    ...mockMeals[1],
    addedToDate: "2024-01-15",
    addedToMealType: "lunch"
  },
  {
    ...mockMeals[3],
    addedToDate: "2024-01-14", 
    addedToMealType: "dinner"
  }
];

export const mockMealPlans = [
  {
    _id: "1",
    date: "2024-01-15",
    meals: [
      {
        mealType: "breakfast",
        meal: mockMeals[6],
        completed: false
      },
      {
        mealType: "lunch", 
        meal: mockMeals[1],
        completed: true
      },
      {
        mealType: "dinner",
        meal: mockMeals[0],
        completed: false
      }
    ]
  }
];

export const mockSavedMealPlans = [
  {
    _id: "template1",
    name: "Healthy Week Plan",
    description: "A balanced meal plan for the whole week",
    createdAt: "2024-01-10",
    meals: mockMealPlans[0].meals
  },
  {
    _id: "template2", 
    name: "Filipino Favorites",
    description: "Traditional Filipino dishes for special occasions",
    createdAt: "2024-01-08",
    meals: [
      { mealType: "lunch", meal: mockMeals[2] },
      { mealType: "dinner", meal: mockMeals[4] }
    ]
  }
];
