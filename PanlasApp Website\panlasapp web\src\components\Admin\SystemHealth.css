.system-health {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.health-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.health-header h2 {
  margin: 0;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.refresh-button {
  padding: 0.5rem 1rem;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.refresh-button:hover {
  background-color: #45a049;
}

.refresh-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.refresh-interval {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-interval select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.last-refreshed {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  text-align: right;
}

.health-section {
  margin-bottom: 2rem;
}

.health-section h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.health-item {
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 1rem;
}

.health-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.health-value {
  font-size: 1.1rem;
  font-weight: 500;
}

.status-healthy {
  color: #4CAF50;
}

.status-unhealthy {
  color: #f44336;
}

.progress-bar {
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background-color: #4CAF50;
}

.progress-text {
  font-size: 0.9rem;
  color: #555;
}

.endpoints-table {
  width: 100%;
  border-collapse: collapse;
}

.endpoints-table th,
.endpoints-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.endpoints-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #333;
}

.endpoints-table tr:hover {
  background-color: #f9f9f9;
}

.errors-section {
  background-color: #fff8f8;
  border-radius: 6px;
  padding: 1rem;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.error-item {
  background-color: #ffebee;
  border-left: 4px solid #f44336;
  padding: 1rem;
  border-radius: 4px;
}

.error-time {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.error-message {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.error-stack {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.8rem;
  overflow-x: auto;
  white-space: pre-wrap;
  margin: 0;
}

.loading-health, .error-health {
  text-align: center;
  padding: 2rem;
}

.error-health {
  color: #f44336;
}
